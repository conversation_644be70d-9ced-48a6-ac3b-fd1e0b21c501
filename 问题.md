# 目前的解题思路简介
- 用户上传图片➡️qwen-vl-plus识别问题类型+题干+选项➡️文字清晰与拼接制作哈希缓存键➡️提交qwen-plus解答➡️将问题内容与正确答案存入数据库➡️返回正确答案给用户

- 用户上传图片➡️qwen-vl-plus识别问题类型+题干+选项➡️文字清晰与拼接制作哈希缓存键➡️redis索引命中➡️返回正确答案给用户


# 实际运营时遇到的一些问题

### 照片识别文字的问题
- qwen大模型或者任何OCR都存在识别失误的问题，比如用户照片模糊的情况下，存在个别文字错别字；

导致的问题：redis的哈希缓存键名，会因为微小差异导致命中失败。再次提交大模型对该问题进行解答。这本身不是问题，但问题是提交大模型解答时有很大概率返回错误答案。另外同一个问题即使内容文字完全一致也会因为选项的排序问题导致出现24个结果。意味着一个问题的错误需要维护24次。维护成本过高。


### 学法减分考试题的问题
- 即使问题的题目完全一致，但实际考题的选项会发生排序的不同。
- 存在题目与选项完全一致，但答案不同的题目。比如；下图的标志含义是什么？问题与选项完全一致，但是图片不同。

导致的问题：即使图片文字识别没有任何问题，也会导致同一个问题有24种选项排列方式。使用题干+选项内容组合制作reids哈希缓存键名的方案。不可靠。8万多个题目量的情况下维护成本太高了。而且问题与答案一致的图片不一致的情况下 一道题24个结果，4道题相同的题。我们组多可能给用户返回96个参考答案，给用户返回的相同问题的参考太多了会影响用户体验。

### qwen-plus的问题
- qwen带上下文记忆能力，且无法移除。
- 本身无法百分百答对问题。

导致的问题，大量的错误，甚至ABCD选项他知道正确答案，但是会使用上次相同问题的记忆来输出options导致最后的错误。尝试很多方法无法解决这个问题。怀疑是开发商恶意机制。

### deepseek的问题
- 本身无法百分百答对问题。
- 请求的响应太慢，平均10秒左右，超出预期。


# 总结
- 需要plan A与plan B的双方案来进行架构，自己题库更高级的检索逻辑，当不存在时使用大模型解答作为保底方案；
- 问题？ 自己检索肯定是个可信阈值的方案。比如相似度。如何控制相似度低于X时调用大模型来保底？会不会导致无法触发大模型保底？或者预期外的大模型保底？

