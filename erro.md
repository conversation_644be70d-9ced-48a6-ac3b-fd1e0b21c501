-------------------------------------
Translated Report (Full Report Below)
-------------------------------------

Process:               Navicat Premium [29519]
Path:                  /Applications/Navicat Premium.app/Contents/MacOS/Navicat Premium
Identifier:            com.navicat.NavicatPremium
Version:               17.1.9 (231)
Code Type:             ARM-64 (Native)
Parent Process:        launchd [1]
User ID:               501

Date/Time:             2025-06-15 19:45:29.1258 +0800
OS Version:            macOS 15.5 (24F74)
Report Version:        12
Anonymous UUID:        A299C36A-E4B7-556D-19DF-1FCE2BA37B42

Sleep/Wake UUID:       0A9E6516-0DC4-42F1-BC99-EEBB17A46072

Time Awake Since Boot: 18000 seconds
Time Since Wake:       1590 seconds

System Integrity Protection: enabled

Crashed Thread:        0  Dispatch queue: com.apple.main-thread

Exception Type:        EXC_BAD_ACCESS (SIGSEGV)
Exception Codes:       KERN_INVALID_ADDRESS at 0x00000659b22dbd00
Exception Codes:       0x0000000000000001, 0x00000659b22dbd00

Termination Reason:    Namespace SIGNAL, Code 11 Segmentation fault: 11
Terminating Process:   exc handler [29519]

VM Region Info: 0x659b22dbd00 is not in any region.  Bytes after previous region: 6501274860801  Bytes before following region: 18988643336960
      REGION TYPE                    START - END         [ VSIZE] PRT/MAX SHRMOD  REGION DETAIL
      commpage (reserved)        1000000000-7000000000   [384.0G] ---/--- SM=NUL  reserved VM address space (unallocated)
--->  GAP OF 0x172ed5850000 BYTES
      VM_ALLOCATE              179ed5850000-179edd850000 [128.0M] ---/rwx SM=NUL  

Thread 0 Crashed::  Dispatch queue: com.apple.main-thread
0   libobjc.A.dylib               	       0x18523f930 objc_retain + 16
1   Navicat Premium               	       0x1051d88ac 0x104b04000 + 7162028
2   Navicat Premium               	       0x1053faaec 0x104b04000 + 9399020
3   Navicat Premium               	       0x104c471f8 0x104b04000 + 1323512
4   Navicat Premium               	       0x105128900 0x104b04000 + 6441216
5   Navicat Premium               	       0x104e2edd0 0x104b04000 + 3321296
6   Foundation                    	       0x186ccf0ac __NSBLOCKOPERATION_IS_CALLING_OUT_TO_A_BLOCK__ + 24
7   Foundation                    	       0x186ccef74 -[NSBlockOperation main] + 96
8   Foundation                    	       0x186ccef0c __NSOPERATION_IS_INVOKING_MAIN__ + 16
9   Foundation                    	       0x186cce27c -[NSOperation start] + 640
10  Foundation                    	       0x186ccdff4 __NSOPERATIONQUEUE_IS_STARTING_AN_OPERATION__ + 16
11  Foundation                    	       0x186ccdee4 __NSOQSchedule_f + 164
12  libdispatch.dylib             	       0x18548a338 _dispatch_block_async_invoke2 + 148
13  libdispatch.dylib             	       0x18549485c _dispatch_client_callout + 16
14  libdispatch.dylib             	       0x1854b1b58 _dispatch_main_queue_drain.cold.5 + 812
15  libdispatch.dylib             	       0x185489db0 _dispatch_main_queue_drain + 180
16  libdispatch.dylib             	       0x185489cec _dispatch_main_queue_callback_4CF + 44
17  CoreFoundation                	       0x18575bda4 __CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__ + 16
18  CoreFoundation                	       0x18571ca9c __CFRunLoopRun + 1980
19  CoreFoundation                	       0x18571bc58 CFRunLoopRunSpecific + 572
20  HIToolbox                     	       0x1911b027c RunCurrentEventLoopInMode + 324
21  HIToolbox                     	       0x1911b34e8 ReceiveNextEventCommon + 676
22  HIToolbox                     	       0x19133e484 _BlockUntilNextEventMatchingListInModeWithFilter + 76
23  AppKit                        	       0x189643ab4 _DPSNextEvent + 684
24  AppKit                        	       0x189fe25b0 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:] + 688
25  AppKit                        	       0x189636c64 -[NSApplication run] + 480
26  AppKit                        	       0x18960d35c NSApplicationMain + 880
27  Navicat Premium               	       0x1055cffac 0x104b04000 + ********
28  dyld                          	       0x185292b98 start + 6076

Thread 1:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libstat.dylib                 	       0x1070154d8 NStat::JobQueue::worker_doJob() + 120
4   libstat.dylib                 	       0x1070159f0 0x10700c000 + 39408
5   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
6   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 2:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libstat.dylib                 	       0x1070154d8 NStat::JobQueue::worker_doJob() + 120
4   libstat.dylib                 	       0x1070159f0 0x10700c000 + 39408
5   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
6   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 3:: com.apple.NSEventThread
0   libsystem_kernel.dylib        	       0x1855f0c34 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x1856033a0 mach_msg2_internal + 76
2   libsystem_kernel.dylib        	       0x1855f9764 mach_msg_overwrite + 484
3   libsystem_kernel.dylib        	       0x1855f0fa8 mach_msg + 24
4   CoreFoundation                	       0x18571de7c __CFRunLoopServiceMachPort + 160
5   CoreFoundation                	       0x18571c798 __CFRunLoopRun + 1208
6   CoreFoundation                	       0x18571bc58 CFRunLoopRunSpecific + 572
7   AppKit                        	       0x1897677fc _NSEventThread + 140
8   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 4:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libcf.dylib                   	       0x106b67d08 0x106b0c000 + 376072
4   libcf.dylib                   	       0x106ba87a0 0x106b0c000 + 640928
5   libcf.dylib                   	       0x106b7d930 0x106b0c000 + 465200
6   libcf.dylib                   	       0x106b7d89c 0x106b0c000 + 465052
7   libcf.dylib                   	       0x106b7d9b8 0x106b0c000 + 465336
8   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 5:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libcf.dylib                   	       0x106b67d08 0x106b0c000 + 376072
4   libcf.dylib                   	       0x106ba87a0 0x106b0c000 + 640928
5   libcf.dylib                   	       0x106b7d930 0x106b0c000 + 465200
6   libcf.dylib                   	       0x106b7d89c 0x106b0c000 + 465052
7   libcf.dylib                   	       0x106b7d9b8 0x106b0c000 + 465336
8   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 6:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libcf.dylib                   	       0x106b67d08 0x106b0c000 + 376072
4   libcf.dylib                   	       0x106ba87a0 0x106b0c000 + 640928
5   libcf.dylib                   	       0x106b7d930 0x106b0c000 + 465200
6   libcf.dylib                   	       0x106b7d89c 0x106b0c000 + 465052
7   libcf.dylib                   	       0x106b7d9b8 0x106b0c000 + 465336
8   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 7:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libcf.dylib                   	       0x106b67d08 0x106b0c000 + 376072
4   libcf.dylib                   	       0x106ba87a0 0x106b0c000 + 640928
5   libcf.dylib                   	       0x106b7d930 0x106b0c000 + 465200
6   libcf.dylib                   	       0x106b7d89c 0x106b0c000 + 465052
7   libcf.dylib                   	       0x106b7d9b8 0x106b0c000 + 465336
8   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 8:: LocalStorage
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   JavaScriptCore                	       0x1a4516a78 WTF::ThreadCondition::timedWait(WTF::Mutex&, WTF::WallTime) + 160
3   JavaScriptCore                	       0x1a44d4cd0 WTF::ParkingLot::parkConditionallyImpl(void const*, WTF::ScopedLambda<bool ()> const&, WTF::ScopedLambda<void ()> const&, WTF::TimeWithDynamicClockType const&) + 1916
4   WebKitLegacy                  	       0x1a18de0c8 WebCore::StorageThread::threadEntryPoint() + 308
5   JavaScriptCore                	       0x1a4513d00 WTF::Thread::entryPoint(WTF::Thread::NewThreadContext*) + 240
6   JavaScriptCore                	       0x1a431fc80 WTF::wtfThreadEntryPoint(void*) + 16
7   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
8   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 9:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libcf.dylib                   	       0x106b67d08 0x106b0c000 + 376072
4   libcf.dylib                   	       0x106ba87a0 0x106b0c000 + 640928
5   libcf.dylib                   	       0x106b7d930 0x106b0c000 + 465200
6   libcf.dylib                   	       0x106b7d89c 0x106b0c000 + 465052
7   libcf.dylib                   	       0x106b7d9b8 0x106b0c000 + 465336
8   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 10:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libcf.dylib                   	       0x106b67d08 0x106b0c000 + 376072
4   libcf.dylib                   	       0x106ba87a0 0x106b0c000 + 640928
5   libcf.dylib                   	       0x106b7d930 0x106b0c000 + 465200
6   libcf.dylib                   	       0x106b7d89c 0x106b0c000 + 465052
7   libcf.dylib                   	       0x106b7d9b8 0x106b0c000 + 465336
8   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 11:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libcf.dylib                   	       0x106b67d08 0x106b0c000 + 376072
4   libcf.dylib                   	       0x106ba87a0 0x106b0c000 + 640928
5   libcf.dylib                   	       0x106b7d930 0x106b0c000 + 465200
6   libcf.dylib                   	       0x106b7d89c 0x106b0c000 + 465052
7   libcf.dylib                   	       0x106b7d9b8 0x106b0c000 + 465336
8   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 12:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libcf.dylib                   	       0x106b67d08 0x106b0c000 + 376072
4   libcf.dylib                   	       0x106ba87a0 0x106b0c000 + 640928
5   libcf.dylib                   	       0x106b7d930 0x106b0c000 + 465200
6   libcf.dylib                   	       0x106b7d89c 0x106b0c000 + 465052
7   libcf.dylib                   	       0x106b7d9b8 0x106b0c000 + 465336
8   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 13:
0   libsystem_pthread.dylib       	       0x18562db6c start_wqthread + 0

Thread 14:
0   libsystem_pthread.dylib       	       0x18562db6c start_wqthread + 0

Thread 15::  Dispatch queue: NSOperationQueue 0x12febb590 (QOS: UNSPECIFIED)
0   libsystem_kernel.dylib        	       0x1855f9498 poll + 8
1   libmariadb.3.dylib            	       0x11fa89f44 pvio_socket_read + 208
2   libmariadb.3.dylib            	       0x11fa961f8 ma_pvio_read + 200
3   libmariadb.3.dylib            	       0x11fa96490 ma_pvio_cache_read + 168
4   libmariadb.3.dylib            	       0x11fa8dbe4 ma_real_read + 84
5   libmariadb.3.dylib            	       0x11fa8d944 ma_net_read + 84
6   libmariadb.3.dylib            	       0x11fa8ece8 ma_net_safe_read + 72
7   libmariadb.3.dylib            	       0x11fa93d7c mthd_my_read_query_result + 80
8   libcc-premium.dylib           	       0x10eeadfb8 0x10ecd0000 + 1957816
9   libcc-premium.dylib           	       0x10fda9ef8 0x10ecd0000 + 17669880
10  libcc-premium.dylib           	       0x10fbb4514 0x10ecd0000 + 15615252
11  libcc-premium.dylib           	       0x10fbb526c 0x10ecd0000 + 15618668
12  libcc-premium.dylib           	       0x10fbb555c 0x10ecd0000 + 15619420
13  libcc-premium.dylib           	       0x10efd9828 0x10ecd0000 + 3184680
14  libcc-premium.dylib           	       0x110411238 CCNavicat::fetchObjects(CMObject*, CMFetchScope, CMObjectType, CMFetchDetailLevel, std::__1::basic_string<char, std::__1::char_traits<char>, std::__1::allocator<char>>, bool) + 404
15  libcc-premium.dylib           	       0x110415fc4 CCNavicat::fetchTables(CMObject*, bool) + 344
16  Navicat Premium               	       0x105d974f8 0x104b04000 + 19477752
17  Foundation                    	       0x186ccf0ac __NSBLOCKOPERATION_IS_CALLING_OUT_TO_A_BLOCK__ + 24
18  Foundation                    	       0x186ccef74 -[NSBlockOperation main] + 96
19  Foundation                    	       0x186ccef0c __NSOPERATION_IS_INVOKING_MAIN__ + 16
20  Foundation                    	       0x186cce27c -[NSOperation start] + 640
21  Foundation                    	       0x186ccdff4 __NSOPERATIONQUEUE_IS_STARTING_AN_OPERATION__ + 16
22  Foundation                    	       0x186ccdee4 __NSOQSchedule_f + 164
23  libdispatch.dylib             	       0x18548a338 _dispatch_block_async_invoke2 + 148
24  libdispatch.dylib             	       0x18549485c _dispatch_client_callout + 16
25  libdispatch.dylib             	       0x18547f5e0 _dispatch_continuation_pop + 596
26  libdispatch.dylib             	       0x18547ec54 _dispatch_async_redirect_invoke + 580
27  libdispatch.dylib             	       0x18548ce30 _dispatch_root_queue_drain + 364
28  libdispatch.dylib             	       0x18548d5d4 _dispatch_worker_thread2 + 156
29  libsystem_pthread.dylib       	       0x18562ee28 _pthread_wqthread + 232
30  libsystem_pthread.dylib       	       0x18562db74 start_wqthread + 8

Thread 16:
0   libsystem_pthread.dylib       	       0x18562db6c start_wqthread + 0

Thread 17:
0   libsystem_pthread.dylib       	       0x18562db6c start_wqthread + 0

Thread 18:
0   libsystem_pthread.dylib       	       0x18562db6c start_wqthread + 0

Thread 19:
0   libsystem_pthread.dylib       	       0x18562db6c start_wqthread + 0

Thread 20:
0   libsystem_pthread.dylib       	       0x18562db6c start_wqthread + 0

Thread 21:
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   libc++.1.dylib                	       0x185563298 std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&) + 32
3   libcf.dylib                   	       0x106b67d08 0x106b0c000 + 376072
4   libcf.dylib                   	       0x106b67c58 0x106b0c000 + 375896
5   libcf.dylib                   	       0x106ba87a0 0x106b0c000 + 640928
6   libcf.dylib                   	       0x106b7d930 0x106b0c000 + 465200
7   libcf.dylib                   	       0x106b7d89c 0x106b0c000 + 465052
8   libcf.dylib                   	       0x106b7d9b8 0x106b0c000 + 465336
9   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
10  libsystem_pthread.dylib       	       0x18562db80 thread_start + 8

Thread 22:: JavaScriptCore libpas scavenger
0   libsystem_kernel.dylib        	       0x1855f43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x1856330e0 _pthread_cond_wait + 984
2   JavaScriptCore                	       0x1a5b42250 scavenger_thread_main + 1584
3   libsystem_pthread.dylib       	       0x185632c0c _pthread_start + 136
4   libsystem_pthread.dylib       	       0x18562db80 thread_start + 8


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x00006000035dbce0   x1: 0x00000001063d5daa   x2: 0x0000000000000000   x3: 0x00006000035dbce0
    x4: 0x0000600001f03f00   x5: 0x0000000000000000   x6: 0x0000000000000000   x7: 0x0000000000000000
    x8: 0x0000000106858000   x9: 0x00000001063d5daa  x10: 0x000000014fdf1e22  x11: 0x000000000000001f
   x12: 0x000000000000000a  x13: 0x000000014f65d7c0  x14: 0x00000001068794c8  x15: 0x00000001068794c8
   x16: 0x00000659b22dbce0  x17: 0x00000659b22dbce0  x18: 0x0000000000000000  x19: 0x000000012fe9d4e0
   x20: 0x0000000000000000  x21: 0x0000000000000000  x22: 0x0000000000000001  x23: 0x000000011ff79be0
   x24: 0x000000011fe55950  x25: 0x0000000000000001  x26: 0x00000000000000b8  x27: 0x00000000000000c0
   x28: 0x00000001064a5e58   fp: 0x000000016b2f97d0   lr: 0x00000001051d88ac
    sp: 0x000000016b2f97a0   pc: 0x000000018523f930 cpsr: 0x00001000
   far: 0x00000659b22dbd00  esr: 0x92000005 (Data Abort) byte read Translation fault

Binary Images:
       0x104b04000 -        0x106493fff com.navicat.NavicatPremium (17.1.9) <c21f9cfb-e5db-33c7-829a-b65324d84bbc> /Applications/Navicat Premium.app/Contents/MacOS/Navicat Premium
       0x106ad8000 -        0x106ae7fff libqexp.dylib (*) <7b2db4e8-a912-344b-9062-9714f8de2a8c> /Applications/Navicat Premium.app/Contents/Frameworks/libqexp.dylib
       0x10ecd0000 -        0x112683fff libcc-premium.dylib (*) <db63f28f-91fe-3289-9636-4d4c5f94359a> /Applications/Navicat Premium.app/Contents/Frameworks/libcc-premium.dylib
       0x106a74000 -        0x106aa3fff libsv.dylib (*) <ec216148-14b6-3f58-9a0e-7ee112abf7e5> /Applications/Navicat Premium.app/Contents/Frameworks/libsv.dylib
       0x106b0c000 -        0x106ce3fff libcf.dylib (*) <31cbf698-4e59-36ce-9996-4fd7f5bc931d> /Applications/Navicat Premium.app/Contents/Frameworks/libcf.dylib
       0x1075b8000 -        0x1077bbfff libmv.dylib (*) <608f7247-d5ec-3d62-9ca2-7c309277c560> /Applications/Navicat Premium.app/Contents/Frameworks/libmv.dylib
       0x106f50000 -        0x106f67fff libndataprofiling.dylib (*) <73c44ec0-2cab-3030-a94c-6a0ec27a2515> /Applications/Navicat Premium.app/Contents/Frameworks/libndataprofiling.dylib
       0x1078e4000 -        0x107a43fff libdv.dylib (*) <bd8d8893-1403-3834-b243-4b8da90aa46f> /Applications/Navicat Premium.app/Contents/Frameworks/libdv.dylib
       0x106de4000 -        0x106e8bfff libvf.dylib (*) <bf6a912b-5773-39a1-bf46-18f162d011b8> /Applications/Navicat Premium.app/Contents/Frameworks/libvf.dylib
       0x107b04000 -        0x107ceffff libncharts.dylib (*) <6faa0f70-e479-3cc3-a48c-bacfa108fe7f> /Applications/Navicat Premium.app/Contents/Frameworks/libncharts.dylib
       0x10700c000 -        0x107027fff libstat.dylib (*) <cb253911-5cb5-30a1-abb3-188f83991ac7> /Applications/Navicat Premium.app/Contents/Frameworks/libstat.dylib
       0x107160000 -        0x1071b3fff libssl.1.1.dylib (*) <dfeec623-13fa-312a-8676-c7485913442d> /Applications/Navicat Premium.app/Contents/Frameworks/libssl.1.1.dylib
       0x1071e8000 -        0x10737bfff libsqlite3mc.0.dylib (*) <a213b9c7-1144-3e62-8eb1-85553a85146f> /Applications/Navicat Premium.app/Contents/Frameworks/libsqlite3mc.0.dylib
       0x1073b0000 -        0x10751bfff libcrypto.1.1.dylib (*) <8b22e8af-b3ff-3329-9a97-6b5d0b8a1426> /Applications/Navicat Premium.app/Contents/Frameworks/libcrypto.1.1.dylib
       0x106fb0000 -        0x106fbffff libaggrpipe.dylib (*) <5618daf1-24b9-3474-8280-4963ca1a7206> /Applications/Navicat Premium.app/Contents/Frameworks/libaggrpipe.dylib
       0x108220000 -        0x108357fff com.prect.EE (1.0) <168a620f-77b7-3558-89c1-1da048b69acd> /Applications/Navicat Premium.app/Contents/Frameworks/EE.framework/Versions/A/EE
       0x107040000 -        0x107097fff com.ridiculousfish.HexFiend-Framework (2.14.1) <09a46843-d9dd-3e7a-8bc8-2a33219fd55f> /Applications/Navicat Premium.app/Contents/Frameworks/HexFiend.framework/Versions/A/HexFiend
       0x106fec000 -        0x106feffff prect.FileMonitor (1.0) <c39efc56-7076-3f3c-8fca-1a9bf12a5128> /Applications/Navicat Premium.app/Contents/Frameworks/FileMonitor.framework/Versions/A/FileMonitor
       0x10843c000 -        0x10856ffff com.prect.qb (1.0) <2aab028f-107c-35c9-8dcb-ed8c8b61940d> /Applications/Navicat Premium.app/Contents/Frameworks/qb.framework/Versions/A/qb
       0x107ebc000 -        0x107eeffff com.prect.NAVTabBarView (1.3.2) <be8b7bed-9a58-340f-9cd4-9878e2fc0cba> /Applications/Navicat Premium.app/Contents/Frameworks/NAVTabBarView.framework/Versions/A/NAVTabBarView
       0x107138000 -        0x10713bfff libXau.6.dylib (*) <82b82ea3-b9c0-3a3b-8269-0e0ffa5c6599> /Applications/Navicat Premium.app/Contents/Frameworks/libXau.6.dylib
       0x107f28000 -        0x107f5ffff libpcre.1.dylib (*) <de8a1fb6-2a20-3679-a46f-709e8f642f54> /Applications/Navicat Premium.app/Contents/Frameworks/libpcre.1.dylib
       0x107dec000 -        0x107e3bfff libturbojpeg.0.dylib (*) <9392e2c3-c338-37bd-9661-97b2ac01cef6> /Applications/Navicat Premium.app/Contents/Frameworks/libturbojpeg.0.dylib
       0x107000000 -        0x107003fff libX11-xcb.1.dylib (*) <6947abbb-9523-3c46-8177-ae8faf172c42> /Applications/Navicat Premium.app/Contents/Frameworks/libX11-xcb.1.dylib
       0x10710c000 -        0x107113fff libcairo-gobject.2.dylib (*) <179ede94-cbd0-346b-998f-a765a0fb88d2> /Applications/Navicat Premium.app/Contents/Frameworks/libcairo-gobject.2.dylib
       0x107e88000 -        0x107e8ffff libXrender.1.dylib (*) <5752a658-9868-3d15-a9d3-36e65392978b> /Applications/Navicat Premium.app/Contents/Frameworks/libXrender.1.dylib
       0x107124000 -        0x107127fff libXdmcp.6.dylib (*) <579d2d33-7d76-3a33-94c2-1a3841bd9abe> /Applications/Navicat Premium.app/Contents/Frameworks/libXdmcp.6.dylib
       0x107e54000 -        0x107e6bfff libcairo-script-interpreter.2.dylib (*) <70c4861d-6ae9-38ca-8e4d-cbfc4180e10e> /Applications/Navicat Premium.app/Contents/Frameworks/libcairo-script-interpreter.2.dylib
       0x1085e0000 -        0x1086a3fff libcairo.2.dylib (*) <7ff3e097-bc60-35e0-a8f3-f04f3549dfb2> /Applications/Navicat Premium.app/Contents/Frameworks/libcairo.2.dylib
       0x107fb0000 -        0x107fc3fff libffi.7.dylib (*) <291aa1cd-ab7e-340c-a068-fed5df7be279> /Applications/Navicat Premium.app/Contents/Frameworks/libffi.7.dylib
       0x108064000 -        0x108093fff libfontconfig.1.dylib (*) <a0bd05c8-4368-3f4e-a1d7-dbf5a7090102> /Applications/Navicat Premium.app/Contents/Frameworks/libfontconfig.1.dylib
       0x107f70000 -        0x107f87fff libxcb.1.dylib (*) <d4d48ae4-c7b2-3f2f-8ee5-3702e3a09a1e> /Applications/Navicat Premium.app/Contents/Frameworks/libxcb.1.dylib
       0x10714c000 -        0x10714ffff libxcb-shm.0.dylib (*) <3c0faece-9eb5-3e3d-bb03-526cb3952bea> /Applications/Navicat Premium.app/Contents/Frameworks/libxcb-shm.0.dylib
       0x1086ec000 -        0x108763fff libfreetype.6.dylib (*) <52cfdea8-de00-33fe-8b52-f4a04dca1251> /Applications/Navicat Premium.app/Contents/Frameworks/libfreetype.6.dylib
       0x108b20000 -        0x108c6bfff libgio-2.0.0.dylib (*) <7a89c05e-d50c-3a0a-8aa3-44b2b1c90482> /Applications/Navicat Premium.app/Contents/Frameworks/libgio-2.0.0.dylib
       0x108028000 -        0x108043fff libfribidi.0.dylib (*) <281036af-0d0a-39a7-95d0-2717f86cb5ac> /Applications/Navicat Premium.app/Contents/Frameworks/libfribidi.0.dylib
       0x1080ac000 -        0x1081abfff libglib-2.0.0.dylib (*) <5138a37e-c5f0-387d-abbf-79cfa0ed32d2> /Applications/Navicat Premium.app/Contents/Frameworks/libglib-2.0.0.dylib
       0x1081e0000 -        0x1081f7fff liblzo2.2.dylib (*) <05799ff8-abad-3631-97eb-fa0c9ea64c65> /Applications/Navicat Premium.app/Contents/Frameworks/liblzo2.2.dylib
       0x107ea0000 -        0x107ea3fff libgmodule-2.0.0.dylib (*) <e3cce3c5-2325-3f87-881d-9c11d1700cf7> /Applications/Navicat Premium.app/Contents/Frameworks/libgmodule-2.0.0.dylib
       0x108848000 -        0x108883fff libgobject-2.0.0.dylib (*) <9c51f20d-6af7-394b-88f8-832e742ce57b> /Applications/Navicat Premium.app/Contents/Frameworks/libgobject-2.0.0.dylib
       0x108000000 -        0x108003fff libgthread-2.0.0.dylib (*) <7d08c954-d690-33d2-b0cb-c6164c47c44d> /Applications/Navicat Premium.app/Contents/Frameworks/libgthread-2.0.0.dylib
       0x107fd4000 -        0x107fdbfff libxcb-render.0.dylib (*) <81cd31c4-22bc-31bc-85ac-7d21dfdbeb71> /Applications/Navicat Premium.app/Contents/Frameworks/libxcb-render.0.dylib
       0x1087d8000 -        0x1087e7fff libharfbuzz-gobject.0.dylib (*) <405f7e3f-a235-38d3-9de7-4cbab7ea5a88> /Applications/Navicat Premium.app/Contents/Frameworks/libharfbuzz-gobject.0.dylib
       0x108014000 -        0x108017fff libharfbuzz-icu.0.dylib (*) <13ebcd15-e831-3556-aea8-e520bdb1ce85> /Applications/Navicat Premium.app/Contents/Frameworks/libharfbuzz-icu.0.dylib
       0x1089a4000 -        0x1089e3fff libjpeg.8.dylib (*) <38838c96-c81b-3d03-8a47-b92e6e70862a> /Applications/Navicat Premium.app/Contents/Frameworks/libjpeg.8.dylib
       0x1088a4000 -        0x108947fff libharfbuzz-subset.0.dylib (*) <4d25f9f6-ff32-33d2-b559-676e13e60cba> /Applications/Navicat Premium.app/Contents/Frameworks/libharfbuzz-subset.0.dylib
       0x1089f8000 -        0x108abffff libharfbuzz.0.dylib (*) <b0985c11-9134-38ff-b765-798df646bbfd> /Applications/Navicat Premium.app/Contents/Frameworks/libharfbuzz.0.dylib
       0x10c460000 -        0x10dfb7fff libicudata.69.dylib (*) <daf5c061-2aff-3d61-8518-be061529e172> /Applications/Navicat Premium.app/Contents/Frameworks/libicudata.69.dylib
       0x10903c000 -        0x10915ffff libicuuc.69.dylib (*) <4c4f633a-32fd-31d9-9ab8-7d8086a99cf9> /Applications/Navicat Premium.app/Contents/Frameworks/libicuuc.69.dylib
       0x1087fc000 -        0x108807fff libintl.8.dylib (*) <0d6faba9-e6b6-36fd-8b1b-aab259632d79> /Applications/Navicat Premium.app/Contents/Frameworks/libintl.8.dylib
       0x10878c000 -        0x10879ffff libgraphite2.3.dylib (*) <cef1d15c-8b87-343e-a5b1-afca6d67421a> /Applications/Navicat Premium.app/Contents/Frameworks/libgraphite2.3.dylib
       0x108dac000 -        0x108de7fff libpango-1.0.0.dylib (*) <53f8ad10-8bd9-358f-9d4b-67237b82403a> /Applications/Navicat Premium.app/Contents/Frameworks/libpango-1.0.0.dylib
       0x108818000 -        0x108827fff libpangocairo-1.0.0.dylib (*) <cc9e12f0-e22e-321a-8988-6ecadbda0bd3> /Applications/Navicat Premium.app/Contents/Frameworks/libpangocairo-1.0.0.dylib
       0x108d40000 -        0x108d4ffff libpangoft2-1.0.0.dylib (*) <a5fd3a07-2e0c-3e64-a1c4-4948f90dde49> /Applications/Navicat Premium.app/Contents/Frameworks/libpangoft2-1.0.0.dylib
       0x1091e4000 -        0x1092b7fff libX11.6.dylib (*) <b888a4b7-e8e9-3860-87f4-d7686b158534> /Applications/Navicat Premium.app/Contents/Frameworks/libX11.6.dylib
       0x108ef8000 -        0x108f3bfff libpixman-1.0.dylib (*) <b331b0d4-6869-39c0-a490-30237d408572> /Applications/Navicat Premium.app/Contents/Frameworks/libpixman-1.0.dylib
       0x108cec000 -        0x108d0ffff libpng16.16.dylib (*) <0101baa0-1fcd-3fe8-8c82-ae132ae210ae> /Applications/Navicat Premium.app/Contents/Frameworks/libpng16.16.dylib
       0x1087b4000 -        0x1087bffff libXext.6.dylib (*) <9a4e59c1-b8cc-3b64-a6fc-88b62d4a0232> /Applications/Navicat Premium.app/Contents/Frameworks/libXext.6.dylib
       0x1094dc000 -        0x109587fff libnpdf.dylib (*) <6149f0c4-b60a-38fb-90d3-d766db0fe741> /Applications/Navicat Premium.app/Contents/Frameworks/libnpdf.dylib
       0x107ff0000 -        0x107ff3fff libboost_system.dylib (*) <4134b1a8-40cf-3d61-80a0-46187fafd3af> /Applications/Navicat Premium.app/Contents/Frameworks/libboost_system.dylib
       0x108208000 -        0x10820bfff libboost_date_time.dylib (*) <dfa7fadf-799b-37e0-ad7e-a9285d7e5e6a> /Applications/Navicat Premium.app/Contents/Frameworks/libboost_date_time.dylib
       0x10898c000 -        0x10898ffff libboost_random.dylib (*) <7dab4b7d-250d-3bc1-a5b7-872a1246bc2a> /Applications/Navicat Premium.app/Contents/Frameworks/libboost_random.dylib
       0x1092e4000 -        0x10936ffff libsioclient_tls.dylib (*) <236d4953-c4ec-3271-a27e-3ac1d6d95b72> /Applications/Navicat Premium.app/Contents/Frameworks/libsioclient_tls.dylib
       0x108afc000 -        0x108b0bfff libhiredis.1.1.0.dylib (*) <8408c2c5-e64a-397f-974d-9b51224f02fc> /Applications/Navicat Premium.app/Contents/Frameworks/libhiredis.1.1.0.dylib
       0x108d24000 -        0x108d27fff libhiredis_ssl.1.1.0.dylib (*) <7fcc419a-08b7-33ec-9924-ef336baf8b39> /Applications/Navicat Premium.app/Contents/Frameworks/libhiredis_ssl.1.1.0.dylib
       0x108e08000 -        0x108e4ffff libredis++.1.dylib (*) <77ac4971-b01c-3398-b3e0-9c89fc5be016> /Applications/Navicat Premium.app/Contents/Frameworks/libredis++.1.dylib
       0x108e88000 -        0x108ed3fff libssh.4.dylib (*) <114a28ee-edf5-3a15-bd11-e76afec1d220> /Applications/Navicat Premium.app/Contents/Frameworks/libssh.4.dylib
       0x108fe0000 -        0x10900bfff libbson-1.0.dylib (*) <358d048e-2851-3c80-8bf9-a631bc678ca6> /Applications/Navicat Premium.app/Contents/Frameworks/libbson-1.0.dylib
       0x1095e8000 -        0x10b12bfff libmozjs-115.dylib (*) <fc29250e-93fb-3444-a4db-e123bd412503> /Applications/Navicat Premium.app/Contents/Frameworks/libmozjs-115.dylib
       0x108d68000 -        0x108d77fff libmdb.3.dylib (*) <79adf2e7-33d9-36fd-a1bc-bd3e332a6a7f> /Applications/Navicat Premium.app/Contents/Frameworks/libmdb.3.dylib
       0x108d8c000 -        0x108d93fff libmdbsql.3.dylib (*) <2db4b555-e732-315c-bf73-8193d84192d0> /Applications/Navicat Premium.app/Contents/Frameworks/libmdbsql.3.dylib
       0x11655c000 -        0x1174f7fff libxl.dylib (*) <68adb112-cb38-3336-8b17-2d530665342d> /Applications/Navicat Premium.app/Contents/Frameworks/libxl.dylib
       0x10b860000 -        0x10b943fff libxml2.2.dylib (*) <92514317-a4d6-3077-981f-e387f3116d0a> /Applications/Navicat Premium.app/Contents/Frameworks/libxml2.2.dylib
       0x108f54000 -        0x108f7ffff libleveldb.1.dylib (*) <913adafe-3218-3a70-806a-f67fa5afb9ee> /Applications/Navicat Premium.app/Contents/Frameworks/libleveldb.1.dylib
       0x109408000 -        0x10947ffff libetpan.20.dylib (*) <72f6683e-c848-3341-a5f9-1f60375e5dc6> /Applications/Navicat Premium.app/Contents/Frameworks/libetpan.20.dylib
       0x10c0b0000 -        0x10c2a7fff libogdf.dylib (*) <ee7e34c7-172f-3432-bf0b-de4ede1700c4> /Applications/Navicat Premium.app/Contents/Frameworks/libogdf.dylib
       0x10b710000 -        0x10b76bfff libtiff.5.dylib (*) <44326523-a9a0-37f3-a6fe-d12e98edcbd4> /Applications/Navicat Premium.app/Contents/Frameworks/libtiff.5.dylib
       0x10bc60000 -        0x10bd4ffff libpodofo.0.9.7.dylib (*) <dfa77ed8-2314-3899-8c0b-b9b7c3c8e75b> /Applications/Navicat Premium.app/Contents/Frameworks/libpodofo.0.9.7.dylib
       0x10b69c000 -        0x10b6c7fff libjpeg.9.dylib (*) <004efb04-d758-34c4-8623-910a561a3b8c> /Applications/Navicat Premium.app/Contents/Frameworks/libjpeg.9.dylib
       0x10b788000 -        0x10b7b7fff libidn.12.dylib (*) <b4b72027-f744-3613-877c-fbc1ed95b75b> /Applications/Navicat Premium.app/Contents/Frameworks/libidn.12.dylib
       0x108fa8000 -        0x108fcffff libConfigurer64.dylib (*) <67bdab44-d7e7-318a-ae6b-9516bd611182> /Applications/Navicat Premium.app/Contents/Resources/libConfigurer64.dylib
       0x10b6d8000 -        0x10b6e3fff libobjc-trampolines.dylib (*) <d02a05cb-6440-3e7e-a02f-931734cab666> /usr/lib/libobjc-trampolines.dylib
       0x11ee38000 -        0x11f56bfff com.apple.AGXMetalG15G-C0 (327.5) <b6366e7e-aa0e-3e38-9f74-cc52bd27ed39> /System/Library/Extensions/AGXMetalG15G_C0.bundle/Contents/MacOS/AGXMetalG15G_C0
       0x11fa84000 -        0x11fac7fff libmariadb.3.dylib (*) <f9347d96-3a1d-333c-973f-8333c9d5dc5a> /Applications/Navicat Premium.app/Contents/Frameworks/libmariadb.3.dylib
       0x13a144000 -        0x13a46ffff libmariadb-10.1.dylib (*) <ccbbc754-9343-3b9e-b4be-431de9ce6686> /Applications/Navicat Premium.app/Contents/Frameworks/libmariadb-10.1.dylib
       0x11fae8000 -        0x11fb23fff libpq.5.dylib (*) <cd3f6711-eb12-3679-bf9d-7b5a82d77db1> /Applications/Navicat Premium.app/Contents/Frameworks/libpq.5.dylib
       0x139944000 -        0x139a4bfff libmongoc-1.0.dylib (*) <ddea68a5-bb7a-3af9-af81-5faa2c4f4dbb> /Applications/Navicat Premium.app/Contents/Frameworks/libmongoc-1.0.dylib
       0x11f9b4000 -        0x11fa5ffff libzstd.1.dylib (*) <2fe6dc27-c431-3ccd-b6c2-525f3eaa7ebe> /Applications/Navicat Premium.app/Contents/Frameworks/libzstd.1.dylib
       0x11fb6c000 -        0x11fb73fff libgssapiv2.2.0.18.so (*) <c34aeccc-53ed-36be-b5c6-87c028a5ecb3> /usr/lib/sasl2/libgssapiv2.2.0.18.so
       0x11fb8c000 -        0x11fb8ffff shadow_auxprop.so (*) <167d354f-4cd8-32f7-b579-c5f2ca199ec3> /usr/lib/sasl2/shadow_auxprop.so
       0x11fba0000 -        0x11fba3fff mschapv2.so (*) <1415fe91-d1ce-3d21-b459-011be78b923b> /usr/lib/sasl2/mschapv2.so
       0x11fbb4000 -        0x11fbb7fff atoken.so (*) <dc184912-7758-3420-96e6-292a28958452> /usr/lib/sasl2/atoken.so
       0x11fbc8000 -        0x11fbcbfff login.so (*) <60cfe40f-b31b-3884-8487-03003875febd> /usr/lib/sasl2/login.so
       0x11fbdc000 -        0x11fbe3fff srp.so (*) <826a0676-93be-3e4b-9ed0-1374aa293710> /usr/lib/sasl2/srp.so
       0x11fbf4000 -        0x11fbf7fff smb_ntlmv2.so (*) <e58d9375-16f8-3d63-84f3-ccdb9d4ac0a3> /usr/lib/sasl2/smb_ntlmv2.so
       0x11fb3c000 -        0x11fb47fff libdigestmd5.2.so (*) <93ec0a5a-556f-39c1-aabf-54f2c89fc252> /usr/lib/sasl2/libdigestmd5.2.so
       0x11fc34000 -        0x11fc3bfff libntlm.so (*) <d8a2f098-3902-38b4-9f19-187ef23bd2d7> /usr/lib/sasl2/libntlm.so
       0x11fb58000 -        0x11fb5bfff plain-clienttoken.so (*) <81e70c0a-e471-3931-9b21-bd90776d8d12> /usr/lib/sasl2/plain-clienttoken.so
       0x11fc4c000 -        0x11fc4ffff libcrammd5.2.so (*) <b65e7318-6de1-38c9-ad35-b0465c0b4076> /usr/lib/sasl2/libcrammd5.2.so
       0x11fc08000 -        0x11fc13fff digestmd5WebDAV.so (*) <bbce28da-29aa-38e0-9279-22c7eb2bf52f> /usr/lib/sasl2/digestmd5WebDAV.so
       0x11fc84000 -        0x11fc87fff apop.so (*) <2491661d-6c5a-3fca-8ec4-5ce61c5d7ba4> /usr/lib/sasl2/apop.so
       0x11fc98000 -        0x11fc9bfff oauthbearer.so (*) <82bd42db-8d42-364a-9035-0474063b2a01> /usr/lib/sasl2/oauthbearer.so
       0x11fcac000 -        0x11fcaffff pwauxprop.so (*) <7935a117-139b-39dc-a14a-0f11943b5554> /usr/lib/sasl2/pwauxprop.so
       0x11fcc0000 -        0x11fcc3fff libplain.2.so (*) <688aaee8-436a-3da5-b0c6-47c73f0acb8c> /usr/lib/sasl2/libplain.2.so
       0x11fcd4000 -        0x11fcd7fff libanonymous.2.so (*) <01de3760-17ec-3495-8fbc-b8584f3e7785> /usr/lib/sasl2/libanonymous.2.so
       0x11fd3c000 -        0x11fd4ffff dhx.so (*) <c182da8a-39db-3d85-b03c-f06e7ee962a9> /usr/lib/sasl2/dhx.so
       0x12fcd8000 -        0x12fd43fff libmongoc-1.0-1.16.dylib (*) <a81350f3-5813-3417-b9f7-da4b28c1d7e8> /Applications/Navicat Premium.app/Contents/Frameworks/libmongoc-1.0-1.16.dylib
       0x11fd68000 -        0x11fdb7fff libtdsodbc.0.so (*) <3a82450a-1a85-363f-af64-1fba596e468a> /Applications/Navicat Premium.app/Contents/Frameworks/libtdsodbc.0.so
       0x11fc60000 -        0x11fc6ffff libiodbcinst.2.dylib (*) <b2873b9d-84b1-393c-80fd-ba18f2722fef> /Applications/Navicat Premium.app/Contents/Frameworks/libiodbcinst.2.dylib
       0x11fce8000 -        0x11fd27fff libiodbc.2.dylib (*) <1b06ae27-315d-3e9a-af80-39d659f8f0f7> /Applications/Navicat Premium.app/Contents/Frameworks/libiodbc.2.dylib
       0x12fdb4000 -        0x12fdb7fff caching_sha2_password.so (*) <58a054ae-cac9-3216-8938-2ac2dd333cdf> /Applications/Navicat Premium.app/Contents/Frameworks/caching_sha2_password.so
       0x185238000 -        0x18528b893 libobjc.A.dylib (*) <4966864d-c147-33d3-bb18-1e3979590b6d> /usr/lib/libobjc.A.dylib
       0x186c91000 -        0x187a7a2ff com.apple.Foundation (6.9) <e8f6a451-0acc-3e05-b18f-fec6618ce44a> /System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
       0x185479000 -        0x1854bf73f libdispatch.dylib (*) <8bf83cda-8db1-3d46-94b0-d811bd77e078> /usr/lib/system/libdispatch.dylib
       0x1856a1000 -        0x185bdffff com.apple.CoreFoundation (6.9) <df489a59-b4f6-32b8-9bb4-9b832960aa52> /System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
       0x1910ed000 -        0x1913f3fdf com.apple.HIToolbox (2.1.1) <9286e29f-fcee-31d0-acea-2842ea23bedf> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
       0x189609000 -        0x18aa9ac7f com.apple.AppKit (6.9) <5d0da1bd-412c-3ed8-84e9-40ca62fe7b42> /System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
       0x18528c000 -        0x1853274cf dyld (*) <9cf0401a-a938-389e-a77d-9e9608076ccf> /usr/lib/dyld
               0x0 - 0xffffffffffffffff ??? (*) <00000000-0000-0000-0000-000000000000> ???
       0x1855f0000 -        0x18562b653 libsystem_kernel.dylib (*) <60485b6f-67e5-38c1-aec9-efd6031ff166> /usr/lib/system/libsystem_kernel.dylib
       0x18562c000 -        0x185638a47 libsystem_pthread.dylib (*) <647b91fc-96d3-3bbb-af08-970df45257c8> /usr/lib/system/libsystem_pthread.dylib
       0x185545000 -        0x1855d1ff7 libc++.1.dylib (*) <875203a1-087b-33a6-93a5-928bb7e9114c> /usr/lib/libc++.1.dylib
       0x1a4319000 -        0x1a5d4fc7f com.apple.JavaScriptCore (20621) <1bce3526-51cf-3c0f-9dc2-6db21a78ef5b> /System/Library/Frameworks/JavaScriptCore.framework/Versions/A/JavaScriptCore
       0x1a1880000 -        0x1a1a9815f com.apple.WebKitLegacy (20621) <52cdc6c0-e3aa-3436-a3b8-44c92d26d6c7> /System/Library/Frameworks/WebKit.framework/Versions/A/Frameworks/WebKitLegacy.framework/Versions/A/WebKitLegacy

External Modification Summary:
  Calls made by other processes targeting this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by all processes on this machine:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0

VM Region Summary:
ReadOnly portion of Libraries: Total=1.8G resident=0K(0%) swapped_out_or_unallocated=1.8G(100%)
Writable regions: Total=5.0G written=4084K(0%) resident=1124K(0%) swapped_out=2960K(0%) unallocated=5.0G(100%)

                                VIRTUAL   REGION 
REGION TYPE                        SIZE    COUNT (non-coalesced) 
===========                     =======  ======= 
Accelerate framework               384K        3 
Activity Tracing                   256K        1 
CG image                          2192K       53 
ColorSync                          736K       35 
CoreAnimation                     54.4M      837 
CoreGraphics                        48K        3 
CoreImage                         1536K       46 
CoreUI image data                 5760K       44 
Foundation                          48K        2 
Kernel Alloc Once                   32K        1 
MALLOC                           801.7M       87 
MALLOC guard page                  384K       24 
STACK GUARD                       56.4M       23 
Stack                             20.3M       43 
VM_ALLOCATE                        2.1G       10 
VM_ALLOCATE (reserved)             3.9G        1         reserved VM address space (unallocated)
WebKit Malloc                    192.2M       10 
__AUTH                            5475K      698 
__AUTH_CONST                      76.6M      939 
__CTF                               824        1 
__DATA                            36.4M     1039 
__DATA_CONST                      34.8M     1053 
__DATA_DIRTY                      2766K      339 
__FONT_DATA                        2352        1 
__INFO_FILTER                         8        1 
__LINKEDIT                       649.8M      112 
__OBJC_RO                         61.4M        1 
__OBJC_RW                         2396K        1 
__TEXT                             1.2G     1038 
__TEXT (graphics)                 58.0M       38 
__TPRO_CONST                       128K        2 
dyld private memory                256K        2 
mapped file                      603.6M       93 
page table in kernel              1124K        1 
shared memory                     1488K       21 
===========                     =======  ======= 
TOTAL                              9.8G     6603 
TOTAL, minus reserved VM space     5.9G     6603 



-----------
Full Report
-----------

{"app_name":"Navicat Premium","timestamp":"2025-06-15 19:45:45.00 +0800","app_version":"17.1.9","slice_uuid":"c21f9cfb-e5db-33c7-829a-b65324d84bbc","build_version":"231","platform":1,"bundleID":"com.navicat.NavicatPremium","share_with_app_devs":1,"is_first_party":0,"bug_type":"309","os_version":"macOS 15.5 (24F74)","roots_installed":0,"name":"Navicat Premium","incident_id":"88ACFF6E-319F-420D-9216-F9B4535CF7D2"}
{
  "uptime" : 18000,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "Mac15,13",
  "coalitionID" : 5867,
  "osVersion" : {
    "train" : "macOS 15.5",
    "build" : "24F74",
    "releaseType" : "User"
  },
  "captureTime" : "2025-06-15 19:45:29.1258 +0800",
  "codeSigningMonitor" : 1,
  "incident" : "88ACFF6E-319F-420D-9216-F9B4535CF7D2",
  "pid" : 29519,
  "translated" : false,
  "cpuType" : "ARM-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-06-15 19:35:47.3281 +0800",
  "procStartAbsTime" : ************,
  "procExitAbsTime" : ************,
  "procName" : "Navicat Premium",
  "procPath" : "\/Applications\/Navicat Premium.app\/Contents\/MacOS\/Navicat Premium",
  "bundleInfo" : {"CFBundleShortVersionString":"17.1.9","CFBundleVersion":"231","CFBundleIdentifier":"com.navicat.NavicatPremium"},
  "storeInfo" : {"deviceIdentifierForVendor":"A6E6586D-8241-56D7-AFF6-324E0B4FF010","thirdParty":true},
  "parentProc" : "launchd",
  "parentPid" : 1,
  "coalitionName" : "com.navicat.NavicatPremium",
  "crashReporterKey" : "A299C36A-E4B7-556D-19DF-1FCE2BA37B42",
  "appleIntelligenceStatus" : {"state":"unavailable","reasons":["countryLocationIneligible","regionIneligible","countryBillingIneligible"]},
  "codeSigningID" : "com.navicat.NavicatPremium",
  "codeSigningTeamID" : "VH7G2MRF27",
  "codeSigningFlags" : 570442241,
  "codeSigningValidationCategory" : 4,
  "codeSigningTrustLevel" : 4294967295,
  "codeSigningAuxiliaryInfo" : 0,
  "instructionByteStream" : {"beforePC":"QYY90CGoEJG8BwAUKOQAFGgOABTAA1\/WAAAA6s3\/\/1QQAED5Ea59kg==","atPC":"MRJA+ZH9Fzbw\/gc2Ef530\/H+\/7QRIODSEQIRq2L+\/1ThAxCqEXywyA=="},
  "bootSessionUUID" : "7780B23D-A190-47A0-9583-98B4532B7C96",
  "wakeTime" : 1590,
  "sleepWakeUUID" : "0A9E6516-0DC4-42F1-BC99-EEBB17A46072",
  "sip" : "enabled",
  "vmRegionInfo" : "0x659b22dbd00 is not in any region.  Bytes after previous region: 6501274860801  Bytes before following region: 18988643336960\n      REGION TYPE                    START - END         [ VSIZE] PRT\/MAX SHRMOD  REGION DETAIL\n      commpage (reserved)        1000000000-7000000000   [384.0G] ---\/--- SM=NUL  reserved VM address space (unallocated)\n--->  GAP OF 0x172ed5850000 BYTES\n      VM_ALLOCATE              179ed5850000-179edd850000 [128.0M] ---\/rwx SM=NUL  ",
  "exception" : {"codes":"0x0000000000000001, 0x00000659b22dbd00","rawCodes":[1,6982311197952],"type":"EXC_BAD_ACCESS","signal":"SIGSEGV","subtype":"KERN_INVALID_ADDRESS at 0x00000659b22dbd00"},
  "termination" : {"flags":0,"code":11,"namespace":"SIGNAL","indicator":"Segmentation fault: 11","byProc":"exc handler","byPid":29519},
  "vmregioninfo" : "0x659b22dbd00 is not in any region.  Bytes after previous region: 6501274860801  Bytes before following region: 18988643336960\n      REGION TYPE                    START - END         [ VSIZE] PRT\/MAX SHRMOD  REGION DETAIL\n      commpage (reserved)        1000000000-7000000000   [384.0G] ---\/--- SM=NUL  reserved VM address space (unallocated)\n--->  GAP OF 0x172ed5850000 BYTES\n      VM_ALLOCATE              179ed5850000-179edd850000 [128.0M] ---\/rwx SM=NUL  ",
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "faultingThread" : 0,
  "threads" : [{"triggered":true,"id":343940,"threadState":{"x":[{"value":105553172741344},{"value":4399652266},{"value":0},{"value":105553172741344},{"value":105553148788480},{"value":0},{"value":0},{"value":0},{"value":4404379648},{"value":4399652266},{"value":5634989602},{"value":31},{"value":10},{"value":5627041728},{"value":4404516040},{"value":4404516040},{"value":6982311197920},{"value":6982311197920},{"value":0},{"value":5098820832},{"value":0},{"value":0},{"value":1},{"value":4831288288},{"value":4830091600},{"value":1},{"value":184},{"value":192},{"value":4400504408}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4380788908},"cpsr":{"value":4096},"fp":{"value":6093248464},"sp":{"value":6093248416},"esr":{"value":2449473541,"description":"(Data Abort) byte read Translation fault"},"pc":{"value":6528694576,"matchesCrashFrame":1},"far":{"value":6982311197952}},"queue":"com.apple.main-thread","frames":[{"imageOffset":31024,"symbol":"objc_retain","symbolLocation":16,"imageIndex":111},{"imageOffset":7162028,"imageIndex":0},{"imageOffset":9399020,"imageIndex":0},{"imageOffset":1323512,"imageIndex":0},{"imageOffset":6441216,"imageIndex":0},{"imageOffset":3321296,"imageIndex":0},{"imageOffset":254124,"symbol":"__NSBLOCKOPERATION_IS_CALLING_OUT_TO_A_BLOCK__","symbolLocation":24,"imageIndex":112},{"imageOffset":253812,"symbol":"-[NSBlockOperation main]","symbolLocation":96,"imageIndex":112},{"imageOffset":253708,"symbol":"__NSOPERATION_IS_INVOKING_MAIN__","symbolLocation":16,"imageIndex":112},{"imageOffset":250492,"symbol":"-[NSOperation start]","symbolLocation":640,"imageIndex":112},{"imageOffset":249844,"symbol":"__NSOPERATIONQUEUE_IS_STARTING_AN_OPERATION__","symbolLocation":16,"imageIndex":112},{"imageOffset":249572,"symbol":"__NSOQSchedule_f","symbolLocation":164,"imageIndex":112},{"imageOffset":70456,"symbol":"_dispatch_block_async_invoke2","symbolLocation":148,"imageIndex":113},{"imageOffset":112732,"symbol":"_dispatch_client_callout","symbolLocation":16,"imageIndex":113},{"imageOffset":232280,"symbol":"_dispatch_main_queue_drain.cold.5","symbolLocation":812,"imageIndex":113},{"imageOffset":69040,"symbol":"_dispatch_main_queue_drain","symbolLocation":180,"imageIndex":113},{"imageOffset":68844,"symbol":"_dispatch_main_queue_callback_4CF","symbolLocation":44,"imageIndex":113},{"imageOffset":765348,"symbol":"__CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__","symbolLocation":16,"imageIndex":114},{"imageOffset":506524,"symbol":"__CFRunLoopRun","symbolLocation":1980,"imageIndex":114},{"imageOffset":502872,"symbol":"CFRunLoopRunSpecific","symbolLocation":572,"imageIndex":114},{"imageOffset":799356,"symbol":"RunCurrentEventLoopInMode","symbolLocation":324,"imageIndex":115},{"imageOffset":812264,"symbol":"ReceiveNextEventCommon","symbolLocation":676,"imageIndex":115},{"imageOffset":2430084,"symbol":"_BlockUntilNextEventMatchingListInModeWithFilter","symbolLocation":76,"imageIndex":115},{"imageOffset":240308,"symbol":"_DPSNextEvent","symbolLocation":684,"imageIndex":116},{"imageOffset":********,"symbol":"-[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]","symbolLocation":688,"imageIndex":116},{"imageOffset":187492,"symbol":"-[NSApplication run]","symbolLocation":480,"imageIndex":116},{"imageOffset":17244,"symbol":"NSApplicationMain","symbolLocation":880,"imageIndex":116},{"imageOffset":********,"imageIndex":0},{"imageOffset":27544,"symbol":"start","symbolLocation":6076,"imageIndex":117}]},{"id":344367,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":38104,"symbol":"NStat::JobQueue::worker_doJob()","symbolLocation":120,"imageIndex":10},{"imageOffset":39408,"imageIndex":10},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":260},{"value":0},{"value":768},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6094958216},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553121247704},{"value":105553121247768},{"value":6094958816},{"value":0},{"value":0},{"value":768},{"value":768},{"value":1280},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6094958336},"sp":{"value":6094958192},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":344368,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":38104,"symbol":"NStat::JobQueue::worker_doJob()","symbolLocation":120,"imageIndex":10},{"imageOffset":39408,"imageIndex":10},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":4},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6095531656},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553121247704},{"value":105553121247768},{"value":6095532256},{"value":0},{"value":0},{"value":1024},{"value":1024},{"value":1536},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6095531776},"sp":{"value":6095531632},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":344383,"name":"com.apple.NSEventThread","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":274890791845888},{"value":0},{"value":274890791845888},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":64003},{"value":0},{"value":18446744073709551569},{"value":8396547768},{"value":0},{"value":4294967295},{"value":2},{"value":274890791845888},{"value":0},{"value":274890791845888},{"value":6096674952},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532641696},"cpsr":{"value":4096},"fp":{"value":6096674800},"sp":{"value":6096674720},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532566068},"far":{"value":0}},"frames":[{"imageOffset":3124,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":119},{"imageOffset":78752,"symbol":"mach_msg2_internal","symbolLocation":76,"imageIndex":119},{"imageOffset":38756,"symbol":"mach_msg_overwrite","symbolLocation":484,"imageIndex":119},{"imageOffset":4008,"symbol":"mach_msg","symbolLocation":24,"imageIndex":119},{"imageOffset":511612,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":160,"imageIndex":114},{"imageOffset":505752,"symbol":"__CFRunLoopRun","symbolLocation":1208,"imageIndex":114},{"imageOffset":502872,"symbol":"CFRunLoopRunSpecific","symbolLocation":572,"imageIndex":114},{"imageOffset":1435644,"symbol":"_NSEventThread","symbolLocation":140,"imageIndex":116},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}]},{"id":347568,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":376072,"imageIndex":4},{"imageOffset":640928,"imageIndex":4},{"imageOffset":465200,"imageIndex":4},{"imageOffset":465052,"imageIndex":4},{"imageOffset":465336,"imageIndex":4},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":4},{"value":0},{"value":5376},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6098398616},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553145343832},{"value":105553131558056},{"value":6098399456},{"value":0},{"value":0},{"value":5376},{"value":5376},{"value":7424},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6098398736},"sp":{"value":6098398592},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":349664,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":376072,"imageIndex":4},{"imageOffset":640928,"imageIndex":4},{"imageOffset":465200,"imageIndex":4},{"imageOffset":465052,"imageIndex":4},{"imageOffset":465336,"imageIndex":4},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":4},{"value":0},{"value":4864},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6100692376},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553145343832},{"value":105553131558056},{"value":6100693216},{"value":0},{"value":0},{"value":4864},{"value":4864},{"value":6912},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6100692496},"sp":{"value":6100692352},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":349679,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":376072,"imageIndex":4},{"imageOffset":640928,"imageIndex":4},{"imageOffset":465200,"imageIndex":4},{"imageOffset":465052,"imageIndex":4},{"imageOffset":465336,"imageIndex":4},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":4},{"value":0},{"value":6400},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6101265816},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553145343832},{"value":105553131558056},{"value":6101266656},{"value":0},{"value":0},{"value":6400},{"value":6400},{"value":8448},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6101265936},"sp":{"value":6101265792},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":349697,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":376072,"imageIndex":4},{"imageOffset":640928,"imageIndex":4},{"imageOffset":465200,"imageIndex":4},{"imageOffset":465052,"imageIndex":4},{"imageOffset":465336,"imageIndex":4},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":4},{"value":0},{"value":5632},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6102412696},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553145343832},{"value":105553131558056},{"value":6102413536},{"value":0},{"value":0},{"value":5632},{"value":5632},{"value":7680},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6102412816},"sp":{"value":6102412672},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":349766,"name":"LocalStorage","threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6105853208},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":5839962352},{"value":5839962416},{"value":6105854176},{"value":0},{"value":0},{"value":0},{"value":1},{"value":256},{"value":8346984296,"symbolLocation":40,"symbol":"_MergedGlobals"},{"value":8346917304,"symbolLocation":0,"symbol":"bmalloc_megapage_table"}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6105853328},"sp":{"value":6105853184},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":2087544,"symbol":"WTF::ThreadCondition::timedWait(WTF::Mutex&, WTF::WallTime)","symbolLocation":160,"imageIndex":122},{"imageOffset":1817808,"symbol":"WTF::ParkingLot::parkConditionallyImpl(void const*, WTF::ScopedLambda<bool ()> const&, WTF::ScopedLambda<void ()> const&, WTF::TimeWithDynamicClockType const&)","symbolLocation":1916,"imageIndex":122},{"imageOffset":385224,"symbol":"WebCore::StorageThread::threadEntryPoint()","symbolLocation":308,"imageIndex":123},{"imageOffset":2075904,"symbol":"WTF::Thread::entryPoint(WTF::Thread::NewThreadContext*)","symbolLocation":240,"imageIndex":122},{"imageOffset":27776,"symbol":"WTF::wtfThreadEntryPoint(void*)","symbolLocation":16,"imageIndex":122},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}]},{"id":350665,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":376072,"imageIndex":4},{"imageOffset":640928,"imageIndex":4},{"imageOffset":465200,"imageIndex":4},{"imageOffset":465052,"imageIndex":4},{"imageOffset":465336,"imageIndex":4},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":4},{"value":0},{"value":6144},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6105279896},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553145343832},{"value":105553131558056},{"value":6105280736},{"value":0},{"value":0},{"value":6144},{"value":6144},{"value":8192},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6105280016},"sp":{"value":6105279872},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":351395,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":376072,"imageIndex":4},{"imageOffset":640928,"imageIndex":4},{"imageOffset":465200,"imageIndex":4},{"imageOffset":465052,"imageIndex":4},{"imageOffset":465336,"imageIndex":4},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":4},{"value":0},{"value":5120},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6093811096},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553145343832},{"value":105553131558056},{"value":6093811936},{"value":0},{"value":0},{"value":5120},{"value":5120},{"value":7168},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6093811216},"sp":{"value":6093811072},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":351416,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":376072,"imageIndex":4},{"imageOffset":640928,"imageIndex":4},{"imageOffset":465200,"imageIndex":4},{"imageOffset":465052,"imageIndex":4},{"imageOffset":465336,"imageIndex":4},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":4},{"value":0},{"value":4864},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6097251736},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553145343832},{"value":105553131558056},{"value":6097252576},{"value":0},{"value":0},{"value":4864},{"value":4864},{"value":6656},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6097251856},"sp":{"value":6097251712},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":351458,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":376072,"imageIndex":4},{"imageOffset":640928,"imageIndex":4},{"imageOffset":465200,"imageIndex":4},{"imageOffset":465052,"imageIndex":4},{"imageOffset":465336,"imageIndex":4},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":260},{"value":0},{"value":5888},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6110440856},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553145343832},{"value":105553131558056},{"value":6110441696},{"value":0},{"value":0},{"value":5888},{"value":5888},{"value":7936},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6110440976},"sp":{"value":6110440832},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":352615,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":120}],"threadState":{"x":[{"value":6099546112},{"value":182347},{"value":6099009536},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6099546112},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532815724},"far":{"value":0}}},{"id":353140,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":120}],"threadState":{"x":[{"value":6100119552},{"value":121139},{"value":6099582976},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6100119552},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532815724},"far":{"value":0}}},{"id":353143,"threadState":{"x":[{"value":4},{"value":0},{"value":4294967295},{"value":128},{"value":0},{"value":0},{"value":39},{"value":105553159547920},{"value":7},{"value":0},{"value":0},{"value":105553159547920},{"value":43},{"value":105553181036964},{"value":2095104},{"value":2043},{"value":230},{"value":4729061092130253776},{"value":0},{"value":16384},{"value":4977868288},{"value":105553144334400},{"value":4294967295},{"value":7},{"value":105553180916448},{"value":4294967296},{"value":0},{"value":105553127008816},{"value":12}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4826111812},"cpsr":{"value":1610616832},"fp":{"value":6113304464},"sp":{"value":6113304384},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532600984},"far":{"value":0}},"queue":"NSOperationQueue 0x12febb590 (QOS: UNSPECIFIED)","frames":[{"imageOffset":38040,"symbol":"poll","symbolLocation":8,"imageIndex":119},{"imageOffset":24388,"symbol":"pvio_socket_read","symbolLocation":208,"imageIndex":83},{"imageOffset":74232,"symbol":"ma_pvio_read","symbolLocation":200,"imageIndex":83},{"imageOffset":74896,"symbol":"ma_pvio_cache_read","symbolLocation":168,"imageIndex":83},{"imageOffset":39908,"symbol":"ma_real_read","symbolLocation":84,"imageIndex":83},{"imageOffset":39236,"symbol":"ma_net_read","symbolLocation":84,"imageIndex":83},{"imageOffset":44264,"symbol":"ma_net_safe_read","symbolLocation":72,"imageIndex":83},{"imageOffset":64892,"symbol":"mthd_my_read_query_result","symbolLocation":80,"imageIndex":83},{"imageOffset":1957816,"imageIndex":2},{"imageOffset":17669880,"imageIndex":2},{"imageOffset":15615252,"imageIndex":2},{"imageOffset":15618668,"imageIndex":2},{"imageOffset":15619420,"imageIndex":2},{"imageOffset":3184680,"imageIndex":2},{"imageOffset":24384056,"symbol":"CCNavicat::fetchObjects(CMObject*, CMFetchScope, CMObjectType, CMFetchDetailLevel, std::__1::basic_string<char, std::__1::char_traits<char>, std::__1::allocator<char>>, bool)","symbolLocation":404,"imageIndex":2},{"imageOffset":24403908,"symbol":"CCNavicat::fetchTables(CMObject*, bool)","symbolLocation":344,"imageIndex":2},{"imageOffset":19477752,"imageIndex":0},{"imageOffset":254124,"symbol":"__NSBLOCKOPERATION_IS_CALLING_OUT_TO_A_BLOCK__","symbolLocation":24,"imageIndex":112},{"imageOffset":253812,"symbol":"-[NSBlockOperation main]","symbolLocation":96,"imageIndex":112},{"imageOffset":253708,"symbol":"__NSOPERATION_IS_INVOKING_MAIN__","symbolLocation":16,"imageIndex":112},{"imageOffset":250492,"symbol":"-[NSOperation start]","symbolLocation":640,"imageIndex":112},{"imageOffset":249844,"symbol":"__NSOPERATIONQUEUE_IS_STARTING_AN_OPERATION__","symbolLocation":16,"imageIndex":112},{"imageOffset":249572,"symbol":"__NSOQSchedule_f","symbolLocation":164,"imageIndex":112},{"imageOffset":70456,"symbol":"_dispatch_block_async_invoke2","symbolLocation":148,"imageIndex":113},{"imageOffset":112732,"symbol":"_dispatch_client_callout","symbolLocation":16,"imageIndex":113},{"imageOffset":26080,"symbol":"_dispatch_continuation_pop","symbolLocation":596,"imageIndex":113},{"imageOffset":23636,"symbol":"_dispatch_async_redirect_invoke","symbolLocation":580,"imageIndex":113},{"imageOffset":81456,"symbol":"_dispatch_root_queue_drain","symbolLocation":364,"imageIndex":113},{"imageOffset":83412,"symbol":"_dispatch_worker_thread2","symbolLocation":156,"imageIndex":113},{"imageOffset":11816,"symbol":"_pthread_wqthread","symbolLocation":232,"imageIndex":120},{"imageOffset":7028,"symbol":"start_wqthread","symbolLocation":8,"imageIndex":120}]},{"id":353516,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":120}],"threadState":{"x":[{"value":6096105472},{"value":233811},{"value":6095568896},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6096105472},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532815724},"far":{"value":0}}},{"id":353517,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":120}],"threadState":{"x":[{"value":6097825792},{"value":189503},{"value":6097289216},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6097825792},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532815724},"far":{"value":0}}},{"id":353518,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":120}],"threadState":{"x":[{"value":6098972672},{"value":104519},{"value":6098436096},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6098972672},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532815724},"far":{"value":0}}},{"id":353519,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":120}],"threadState":{"x":[{"value":6104707072},{"value":144659},{"value":6104170496},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6104707072},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532815724},"far":{"value":0}}},{"id":353643,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":120}],"threadState":{"x":[{"value":6118649856},{"value":239535},{"value":6118113280},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6118649856},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532815724},"far":{"value":0}}},{"id":353669,"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":123544,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":32,"imageIndex":121},{"imageOffset":376072,"imageIndex":4},{"imageOffset":375896,"imageIndex":4},{"imageOffset":640928,"imageIndex":4},{"imageOffset":465200,"imageIndex":4},{"imageOffset":465052,"imageIndex":4},{"imageOffset":465336,"imageIndex":4},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}],"threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6120369512},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":105553144190712},{"value":105553198967752},{"value":6120370400},{"value":0},{"value":0},{"value":0},{"value":1},{"value":256},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6120369632},"sp":{"value":6120369488},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}}},{"id":353670,"name":"JavaScriptCore libpas scavenger","threadState":{"x":[{"value":260},{"value":0},{"value":13312},{"value":0},{"value":0},{"value":160},{"value":9},{"value":999998008},{"value":6120943272},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8396545888},{"value":0},{"value":5677230272},{"value":5677230336},{"value":6120943840},{"value":999998008},{"value":9},{"value":13312},{"value":18945},{"value":19200},{"value":4652007308841189376},{"value":8347070464,"symbolLocation":0,"symbol":"pas_first_shared_page_directory"}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532837600},"cpsr":{"value":1610616832},"fp":{"value":6120943392},"sp":{"value":6120943248},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6532580300},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":119},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":120},{"imageOffset":25334352,"symbol":"scavenger_thread_main","symbolLocation":1584,"imageIndex":122},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":120},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":120}]}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4373626880,
    "CFBundleShortVersionString" : "17.1.9",
    "CFBundleIdentifier" : "com.navicat.NavicatPremium",
    "size" : 26804224,
    "uuid" : "c21f9cfb-e5db-33c7-829a-b65324d84bbc",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/MacOS\/Navicat Premium",
    "name" : "Navicat Premium",
    "CFBundleVersion" : "231"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4407001088,
    "size" : 65536,
    "uuid" : "7b2db4e8-a912-344b-9062-9714f8de2a8c",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libqexp.dylib",
    "name" : "libqexp.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4543283200,
    "size" : 60506112,
    "uuid" : "db63f28f-91fe-3289-9636-4d4c5f94359a",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libcc-premium.dylib",
    "name" : "libcc-premium.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4406591488,
    "size" : 196608,
    "uuid" : "ec216148-14b6-3f58-9a0e-7ee112abf7e5",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libsv.dylib",
    "name" : "libsv.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4407214080,
    "size" : 1933312,
    "uuid" : "31cbf698-4e59-36ce-9996-4fd7f5bc931d",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libcf.dylib",
    "name" : "libcf.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4418404352,
    "size" : 2113536,
    "uuid" : "608f7247-d5ec-3d62-9ca2-7c309277c560",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libmv.dylib",
    "name" : "libmv.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4411686912,
    "size" : 98304,
    "uuid" : "73c44ec0-2cab-3030-a94c-6a0ec27a2515",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libndataprofiling.dylib",
    "name" : "libndataprofiling.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4421730304,
    "size" : 1441792,
    "uuid" : "bd8d8893-1403-3834-b243-4b8da90aa46f",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libdv.dylib",
    "name" : "libdv.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4410195968,
    "size" : 688128,
    "uuid" : "bf6a912b-5773-39a1-bf46-18f162d011b8",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libvf.dylib",
    "name" : "libvf.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4423958528,
    "size" : 2015232,
    "uuid" : "6faa0f70-e479-3cc3-a48c-bacfa108fe7f",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libncharts.dylib",
    "name" : "libncharts.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4412456960,
    "size" : 114688,
    "uuid" : "cb253911-5cb5-30a1-abb3-188f83991ac7",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libstat.dylib",
    "name" : "libstat.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4413849600,
    "size" : 344064,
    "uuid" : "dfeec623-13fa-312a-8676-c7485913442d",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libssl.1.1.dylib",
    "name" : "libssl.1.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4414406656,
    "size" : 1654784,
    "uuid" : "a213b9c7-1144-3e62-8eb1-85553a85146f",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libsqlite3mc.0.dylib",
    "name" : "libsqlite3mc.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4416274432,
    "size" : 1490944,
    "uuid" : "8b22e8af-b3ff-3329-9a97-6b5d0b8a1426",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libcrypto.1.1.dylib",
    "name" : "libcrypto.1.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4412080128,
    "size" : 65536,
    "uuid" : "5618daf1-24b9-3474-8280-4963ca1a7206",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libaggrpipe.dylib",
    "name" : "libaggrpipe.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4431413248,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.prect.EE",
    "size" : 1277952,
    "uuid" : "168a620f-77b7-3558-89c1-1da048b69acd",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/EE.framework\/Versions\/A\/EE",
    "name" : "EE",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4412669952,
    "CFBundleShortVersionString" : "2.14.1",
    "CFBundleIdentifier" : "com.ridiculousfish.HexFiend-Framework",
    "size" : 360448,
    "uuid" : "09a46843-d9dd-3e7a-8bc8-2a33219fd55f",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/HexFiend.framework\/Versions\/A\/HexFiend",
    "name" : "HexFiend",
    "CFBundleVersion" : "200"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4412325888,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "prect.FileMonitor",
    "size" : 16384,
    "uuid" : "c39efc56-7076-3f3c-8fca-1a9bf12a5128",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/FileMonitor.framework\/Versions\/A\/FileMonitor",
    "name" : "FileMonitor",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4433625088,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.prect.qb",
    "size" : 1261568,
    "uuid" : "2aab028f-107c-35c9-8dcb-ed8c8b61940d",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/qb.framework\/Versions\/A\/qb",
    "name" : "qb",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4427857920,
    "CFBundleShortVersionString" : "1.3.2",
    "CFBundleIdentifier" : "com.prect.NAVTabBarView",
    "size" : 212992,
    "uuid" : "be8b7bed-9a58-340f-9cd4-9878e2fc0cba",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/NAVTabBarView.framework\/Versions\/A\/NAVTabBarView",
    "name" : "NAVTabBarView",
    "CFBundleVersion" : "1.3.2"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4413685760,
    "size" : 16384,
    "uuid" : "82b82ea3-b9c0-3a3b-8269-0e0ffa5c6599",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libXau.6.dylib",
    "name" : "libXau.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4428300288,
    "size" : 229376,
    "uuid" : "de8a1fb6-2a20-3679-a46f-709e8f642f54",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libpcre.1.dylib",
    "name" : "libpcre.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4427005952,
    "size" : 327680,
    "uuid" : "9392e2c3-c338-37bd-9661-97b2ac01cef6",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libturbojpeg.0.dylib",
    "name" : "libturbojpeg.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4412407808,
    "size" : 16384,
    "uuid" : "6947abbb-9523-3c46-8177-ae8faf172c42",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libX11-xcb.1.dylib",
    "name" : "libX11-xcb.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4413505536,
    "size" : 32768,
    "uuid" : "179ede94-cbd0-346b-998f-a765a0fb88d2",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libcairo-gobject.2.dylib",
    "name" : "libcairo-gobject.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4427644928,
    "size" : 32768,
    "uuid" : "5752a658-9868-3d15-a9d3-36e65392978b",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libXrender.1.dylib",
    "name" : "libXrender.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4413603840,
    "size" : 16384,
    "uuid" : "579d2d33-7d76-3a33-94c2-1a3841bd9abe",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libXdmcp.6.dylib",
    "name" : "libXdmcp.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4427431936,
    "size" : 98304,
    "uuid" : "70c4861d-6ae9-38ca-8e4d-cbfc4180e10e",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libcairo-script-interpreter.2.dylib",
    "name" : "libcairo-script-interpreter.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4435345408,
    "size" : 802816,
    "uuid" : "7ff3e097-bc60-35e0-a8f3-f04f3549dfb2",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libcairo.2.dylib",
    "name" : "libcairo.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4428857344,
    "size" : 81920,
    "uuid" : "291aa1cd-ab7e-340c-a068-fed5df7be279",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libffi.7.dylib",
    "name" : "libffi.7.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4429594624,
    "size" : 196608,
    "uuid" : "a0bd05c8-4368-3f4e-a1d7-dbf5a7090102",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libfontconfig.1.dylib",
    "name" : "libfontconfig.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4428595200,
    "size" : 98304,
    "uuid" : "d4d48ae4-c7b2-3f2f-8ee5-3702e3a09a1e",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libxcb.1.dylib",
    "name" : "libxcb.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4413767680,
    "size" : 16384,
    "uuid" : "3c0faece-9eb5-3e3d-bb03-526cb3952bea",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libxcb-shm.0.dylib",
    "name" : "libxcb-shm.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4436443136,
    "size" : 491520,
    "uuid" : "52cfdea8-de00-33fe-8b52-f4a04dca1251",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libfreetype.6.dylib",
    "name" : "libfreetype.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4440850432,
    "size" : 1359872,
    "uuid" : "7a89c05e-d50c-3a0a-8aa3-44b2b1c90482",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libgio-2.0.0.dylib",
    "name" : "libgio-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4429348864,
    "size" : 114688,
    "uuid" : "281036af-0d0a-39a7-95d0-2717f86cb5ac",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libfribidi.0.dylib",
    "name" : "libfribidi.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4429889536,
    "size" : 1048576,
    "uuid" : "5138a37e-c5f0-387d-abbf-79cfa0ed32d2",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libglib-2.0.0.dylib",
    "name" : "libglib-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4431151104,
    "size" : 98304,
    "uuid" : "05799ff8-abad-3631-97eb-fa0c9ea64c65",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/liblzo2.2.dylib",
    "name" : "liblzo2.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4427743232,
    "size" : 16384,
    "uuid" : "e3cce3c5-2325-3f87-881d-9c11d1700cf7",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libgmodule-2.0.0.dylib",
    "name" : "libgmodule-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4437868544,
    "size" : 245760,
    "uuid" : "9c51f20d-6af7-394b-88f8-832e742ce57b",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libgobject-2.0.0.dylib",
    "name" : "libgobject-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4429185024,
    "size" : 16384,
    "uuid" : "7d08c954-d690-33d2-b0cb-c6164c47c44d",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libgthread-2.0.0.dylib",
    "name" : "libgthread-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4429004800,
    "size" : 32768,
    "uuid" : "81cd31c4-22bc-31bc-85ac-7d21dfdbeb71",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libxcb-render.0.dylib",
    "name" : "libxcb-render.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4437409792,
    "size" : 65536,
    "uuid" : "405f7e3f-a235-38d3-9de7-4cbab7ea5a88",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libharfbuzz-gobject.0.dylib",
    "name" : "libharfbuzz-gobject.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4429266944,
    "size" : 16384,
    "uuid" : "13ebcd15-e831-3556-aea8-e520bdb1ce85",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libharfbuzz-icu.0.dylib",
    "name" : "libharfbuzz-icu.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4439293952,
    "size" : 262144,
    "uuid" : "38838c96-c81b-3d03-8a47-b92e6e70862a",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libjpeg.8.dylib",
    "name" : "libjpeg.8.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4438245376,
    "size" : 671744,
    "uuid" : "4d25f9f6-ff32-33d2-b559-676e13e60cba",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libharfbuzz-subset.0.dylib",
    "name" : "libharfbuzz-subset.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4439638016,
    "size" : 819200,
    "uuid" : "b0985c11-9134-38ff-b765-798df646bbfd",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libharfbuzz.0.dylib",
    "name" : "libharfbuzz.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4500881408,
    "size" : 28672000,
    "uuid" : "daf5c061-2aff-3d61-8518-be061529e172",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libicudata.69.dylib",
    "name" : "libicudata.69.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4446208000,
    "size" : 1196032,
    "uuid" : "4c4f633a-32fd-31d9-9ab8-7d8086a99cf9",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libicuuc.69.dylib",
    "name" : "libicuuc.69.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4437557248,
    "size" : 49152,
    "uuid" : "0d6faba9-e6b6-36fd-8b1b-aab259632d79",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libintl.8.dylib",
    "name" : "libintl.8.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4437098496,
    "size" : 81920,
    "uuid" : "cef1d15c-8b87-343e-a5b1-afca6d67421a",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libgraphite2.3.dylib",
    "name" : "libgraphite2.3.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4443521024,
    "size" : 245760,
    "uuid" : "53f8ad10-8bd9-358f-9d4b-67237b82403a",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libpango-1.0.0.dylib",
    "name" : "libpango-1.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4437671936,
    "size" : 65536,
    "uuid" : "cc9e12f0-e22e-321a-8988-6ecadbda0bd3",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libpangocairo-1.0.0.dylib",
    "name" : "libpangocairo-1.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4443078656,
    "size" : 65536,
    "uuid" : "a5fd3a07-2e0c-3e64-a1c4-4948f90dde49",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libpangoft2-1.0.0.dylib",
    "name" : "libpangoft2-1.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4447944704,
    "size" : 868352,
    "uuid" : "b888a4b7-e8e9-3860-87f4-d7686b158534",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libX11.6.dylib",
    "name" : "libX11.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4444880896,
    "size" : 278528,
    "uuid" : "b331b0d4-6869-39c0-a490-30237d408572",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libpixman-1.0.dylib",
    "name" : "libpixman-1.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4442734592,
    "size" : 147456,
    "uuid" : "0101baa0-1fcd-3fe8-8c82-ae132ae210ae",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libpng16.16.dylib",
    "name" : "libpng16.16.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4437262336,
    "size" : 49152,
    "uuid" : "9a4e59c1-b8cc-3b64-a6fc-88b62d4a0232",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libXext.6.dylib",
    "name" : "libXext.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4451057664,
    "size" : 704512,
    "uuid" : "6149f0c4-b60a-38fb-90d3-d766db0fe741",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libnpdf.dylib",
    "name" : "libnpdf.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4429119488,
    "size" : 16384,
    "uuid" : "4134b1a8-40cf-3d61-80a0-46187fafd3af",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libboost_system.dylib",
    "name" : "libboost_system.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4431314944,
    "size" : 16384,
    "uuid" : "dfa7fadf-799b-37e0-ad7e-a9285d7e5e6a",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libboost_date_time.dylib",
    "name" : "libboost_date_time.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4439195648,
    "size" : 16384,
    "uuid" : "7dab4b7d-250d-3bc1-a5b7-872a1246bc2a",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libboost_random.dylib",
    "name" : "libboost_random.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4448993280,
    "size" : 573440,
    "uuid" : "236d4953-c4ec-3271-a27e-3ac1d6d95b72",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libsioclient_tls.dylib",
    "name" : "libsioclient_tls.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4440702976,
    "size" : 65536,
    "uuid" : "8408c2c5-e64a-397f-974d-9b51224f02fc",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libhiredis.1.1.0.dylib",
    "name" : "libhiredis.1.1.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4442963968,
    "size" : 16384,
    "uuid" : "7fcc419a-08b7-33ec-9924-ef336baf8b39",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libhiredis_ssl.1.1.0.dylib",
    "name" : "libhiredis_ssl.1.1.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4443897856,
    "size" : 294912,
    "uuid" : "77ac4971-b01c-3398-b3e0-9c89fc5be016",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libredis++.1.dylib",
    "name" : "libredis++.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4444422144,
    "size" : 311296,
    "uuid" : "114a28ee-edf5-3a15-bd11-e76afec1d220",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libssh.4.dylib",
    "name" : "libssh.4.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4445831168,
    "size" : 180224,
    "uuid" : "358d048e-2851-3c80-8bf9-a631bc678ca6",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libbson-1.0.dylib",
    "name" : "libbson-1.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4452155392,
    "size" : 28590080,
    "uuid" : "fc29250e-93fb-3444-a4db-e123bd412503",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libmozjs-115.dylib",
    "name" : "libmozjs-115.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4443242496,
    "size" : 65536,
    "uuid" : "79adf2e7-33d9-36fd-a1bc-bd3e332a6a7f",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libmdb.3.dylib",
    "name" : "libmdb.3.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4443389952,
    "size" : 32768,
    "uuid" : "2db4b555-e732-315c-bf73-8193d84192d0",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libmdbsql.3.dylib",
    "name" : "libmdbsql.3.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4669685760,
    "size" : 16367616,
    "uuid" : "68adb112-cb38-3336-8b17-2d530665342d",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libxl.dylib",
    "name" : "libxl.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4488298496,
    "size" : 933888,
    "uuid" : "92514317-a4d6-3077-981f-e387f3116d0a",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libxml2.2.dylib",
    "name" : "libxml2.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4445257728,
    "size" : 180224,
    "uuid" : "913adafe-3218-3a70-806a-f67fa5afb9ee",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libleveldb.1.dylib",
    "name" : "libleveldb.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4450189312,
    "size" : 491520,
    "uuid" : "72f6683e-c848-3341-a5f9-1f60375e5dc6",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libetpan.20.dylib",
    "name" : "libetpan.20.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4497014784,
    "size" : 2064384,
    "uuid" : "ee7e34c7-172f-3432-bf0b-de4ede1700c4",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libogdf.dylib",
    "name" : "libogdf.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4486922240,
    "size" : 376832,
    "uuid" : "44326523-a9a0-37f3-a6fe-d12e98edcbd4",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libtiff.5.dylib",
    "name" : "libtiff.5.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4492492800,
    "size" : 983040,
    "uuid" : "dfa77ed8-2314-3899-8c0b-b9b7c3c8e75b",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libpodofo.0.9.7.dylib",
    "name" : "libpodofo.0.9.7.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4486447104,
    "size" : 180224,
    "uuid" : "004efb04-d758-34c4-8623-910a561a3b8c",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libjpeg.9.dylib",
    "name" : "libjpeg.9.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4487413760,
    "size" : 196608,
    "uuid" : "b4b72027-f744-3613-877c-fbc1ed95b75b",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libidn.12.dylib",
    "name" : "libidn.12.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4445601792,
    "size" : 163840,
    "uuid" : "67bdab44-d7e7-318a-ae6b-9516bd611182",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Resources\/libConfigurer64.dylib",
    "name" : "libConfigurer64.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4486692864,
    "size" : 49152,
    "uuid" : "d02a05cb-6440-3e7e-a02f-931734cab666",
    "path" : "\/usr\/lib\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4813193216,
    "CFBundleShortVersionString" : "327.5",
    "CFBundleIdentifier" : "com.apple.AGXMetalG15G-C0",
    "size" : 7553024,
    "uuid" : "b6366e7e-aa0e-3e38-9f74-cc52bd27ed39",
    "path" : "\/System\/Library\/Extensions\/AGXMetalG15G_C0.bundle\/Contents\/MacOS\/AGXMetalG15G_C0",
    "name" : "AGXMetalG15G_C0",
    "CFBundleVersion" : "327.5"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4826087424,
    "size" : 278528,
    "uuid" : "f9347d96-3a1d-333c-973f-8333c9d5dc5a",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libmariadb.3.dylib",
    "name" : "libmariadb.3.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 5269372928,
    "size" : 3325952,
    "uuid" : "ccbbc754-9343-3b9e-b4be-431de9ce6686",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libmariadb-10.1.dylib",
    "name" : "libmariadb-10.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4826497024,
    "size" : 245760,
    "uuid" : "cd3f6711-eb12-3679-bf9d-7b5a82d77db1",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libpq.5.dylib",
    "name" : "libpq.5.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 5260984320,
    "size" : 1081344,
    "uuid" : "ddea68a5-bb7a-3af9-af81-5faa2c4f4dbb",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libmongoc-1.0.dylib",
    "name" : "libmongoc-1.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4825235456,
    "size" : 704512,
    "uuid" : "2fe6dc27-c431-3ccd-b6c2-525f3eaa7ebe",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libzstd.1.dylib",
    "name" : "libzstd.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4827037696,
    "size" : 32768,
    "uuid" : "c34aeccc-53ed-36be-b5c6-87c028a5ecb3",
    "path" : "\/usr\/lib\/sasl2\/libgssapiv2.2.0.18.so",
    "name" : "libgssapiv2.2.0.18.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4827168768,
    "size" : 16384,
    "uuid" : "167d354f-4cd8-32f7-b579-c5f2ca199ec3",
    "path" : "\/usr\/lib\/sasl2\/shadow_auxprop.so",
    "name" : "shadow_auxprop.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4827250688,
    "size" : 16384,
    "uuid" : "1415fe91-d1ce-3d21-b459-011be78b923b",
    "path" : "\/usr\/lib\/sasl2\/mschapv2.so",
    "name" : "mschapv2.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4827332608,
    "size" : 16384,
    "uuid" : "dc184912-7758-3420-96e6-292a28958452",
    "path" : "\/usr\/lib\/sasl2\/atoken.so",
    "name" : "atoken.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4827414528,
    "size" : 16384,
    "uuid" : "60cfe40f-b31b-3884-8487-03003875febd",
    "path" : "\/usr\/lib\/sasl2\/login.so",
    "name" : "login.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4827496448,
    "size" : 32768,
    "uuid" : "826a0676-93be-3e4b-9ed0-1374aa293710",
    "path" : "\/usr\/lib\/sasl2\/srp.so",
    "name" : "srp.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4827594752,
    "size" : 16384,
    "uuid" : "e58d9375-16f8-3d63-84f3-ccdb9d4ac0a3",
    "path" : "\/usr\/lib\/sasl2\/smb_ntlmv2.so",
    "name" : "smb_ntlmv2.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4826841088,
    "size" : 49152,
    "uuid" : "93ec0a5a-556f-39c1-aabf-54f2c89fc252",
    "path" : "\/usr\/lib\/sasl2\/libdigestmd5.2.so",
    "name" : "libdigestmd5.2.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4827856896,
    "size" : 32768,
    "uuid" : "d8a2f098-3902-38b4-9f19-187ef23bd2d7",
    "path" : "\/usr\/lib\/sasl2\/libntlm.so",
    "name" : "libntlm.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4826955776,
    "size" : 16384,
    "uuid" : "81e70c0a-e471-3931-9b21-bd90776d8d12",
    "path" : "\/usr\/lib\/sasl2\/plain-clienttoken.so",
    "name" : "plain-clienttoken.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4827955200,
    "size" : 16384,
    "uuid" : "b65e7318-6de1-38c9-ad35-b0465c0b4076",
    "path" : "\/usr\/lib\/sasl2\/libcrammd5.2.so",
    "name" : "libcrammd5.2.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4827676672,
    "size" : 49152,
    "uuid" : "bbce28da-29aa-38e0-9279-22c7eb2bf52f",
    "path" : "\/usr\/lib\/sasl2\/digestmd5WebDAV.so",
    "name" : "digestmd5WebDAV.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4828184576,
    "size" : 16384,
    "uuid" : "2491661d-6c5a-3fca-8ec4-5ce61c5d7ba4",
    "path" : "\/usr\/lib\/sasl2\/apop.so",
    "name" : "apop.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4828266496,
    "size" : 16384,
    "uuid" : "82bd42db-8d42-364a-9035-0474063b2a01",
    "path" : "\/usr\/lib\/sasl2\/oauthbearer.so",
    "name" : "oauthbearer.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4828348416,
    "size" : 16384,
    "uuid" : "7935a117-139b-39dc-a14a-0f11943b5554",
    "path" : "\/usr\/lib\/sasl2\/pwauxprop.so",
    "name" : "pwauxprop.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4828430336,
    "size" : 16384,
    "uuid" : "688aaee8-436a-3da5-b0c6-47c73f0acb8c",
    "path" : "\/usr\/lib\/sasl2\/libplain.2.so",
    "name" : "libplain.2.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4828512256,
    "size" : 16384,
    "uuid" : "01de3760-17ec-3495-8fbc-b8584f3e7785",
    "path" : "\/usr\/lib\/sasl2\/libanonymous.2.so",
    "name" : "libanonymous.2.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4828938240,
    "size" : 81920,
    "uuid" : "c182da8a-39db-3d85-b03c-f06e7ee962a9",
    "path" : "\/usr\/lib\/sasl2\/dhx.so",
    "name" : "dhx.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 5096964096,
    "size" : 442368,
    "uuid" : "a81350f3-5813-3417-b9f7-da4b28c1d7e8",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libmongoc-1.0-1.16.dylib",
    "name" : "libmongoc-1.0-1.16.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4829118464,
    "size" : 327680,
    "uuid" : "3a82450a-1a85-363f-af64-1fba596e468a",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libtdsodbc.0.so",
    "name" : "libtdsodbc.0.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4828037120,
    "size" : 65536,
    "uuid" : "b2873b9d-84b1-393c-80fd-ba18f2722fef",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libiodbcinst.2.dylib",
    "name" : "libiodbcinst.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4828594176,
    "size" : 262144,
    "uuid" : "1b06ae27-315d-3e9a-af80-39d659f8f0f7",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/libiodbc.2.dylib",
    "name" : "libiodbc.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 5097865216,
    "size" : 16384,
    "uuid" : "58a054ae-cac9-3216-8938-2ac2dd333cdf",
    "path" : "\/Applications\/Navicat Premium.app\/Contents\/Frameworks\/caching_sha2_password.so",
    "name" : "caching_sha2_password.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6528663552,
    "size" : 342164,
    "uuid" : "4966864d-c147-33d3-bb18-1e3979590b6d",
    "path" : "\/usr\/lib\/libobjc.A.dylib",
    "name" : "libobjc.A.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6556291072,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.Foundation",
    "size" : 14586624,
    "uuid" : "e8f6a451-0acc-3e05-b18f-fec6618ce44a",
    "path" : "\/System\/Library\/Frameworks\/Foundation.framework\/Versions\/C\/Foundation",
    "name" : "Foundation",
    "CFBundleVersion" : "3502.1.401"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6531026944,
    "size" : 288576,
    "uuid" : "8bf83cda-8db1-3d46-94b0-d811bd77e078",
    "path" : "\/usr\/lib\/system\/libdispatch.dylib",
    "name" : "libdispatch.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6533287936,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 5500928,
    "uuid" : "df489a59-b4f6-32b8-9bb4-9b832960aa52",
    "path" : "\/System\/Library\/Frameworks\/CoreFoundation.framework\/Versions\/A\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "3502.1.401"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6728634368,
    "CFBundleShortVersionString" : "2.1.1",
    "CFBundleIdentifier" : "com.apple.HIToolbox",
    "size" : 3174368,
    "uuid" : "9286e29f-fcee-31d0-acea-2842ea23bedf",
    "path" : "\/System\/Library\/Frameworks\/Carbon.framework\/Versions\/A\/Frameworks\/HIToolbox.framework\/Versions\/A\/HIToolbox",
    "name" : "HIToolbox"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6599774208,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.AppKit",
    "size" : 21568640,
    "uuid" : "5d0da1bd-412c-3ed8-84e9-40ca62fe7b42",
    "path" : "\/System\/Library\/Frameworks\/AppKit.framework\/Versions\/C\/AppKit",
    "name" : "AppKit",
    "CFBundleVersion" : "2575.60.5"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6529007616,
    "size" : 636112,
    "uuid" : "9cf0401a-a938-389e-a77d-9e9608076ccf",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6532562944,
    "size" : 243284,
    "uuid" : "60485b6f-67e5-38c1-aec9-efd6031ff166",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6532808704,
    "size" : 51784,
    "uuid" : "647b91fc-96d3-3bbb-af08-970df45257c8",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6531862528,
    "size" : 577528,
    "uuid" : "875203a1-087b-33a6-93a5-928bb7e9114c",
    "path" : "\/usr\/lib\/libc++.1.dylib",
    "name" : "libc++.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 7049678848,
    "CFBundleShortVersionString" : "20621",
    "CFBundleIdentifier" : "com.apple.JavaScriptCore",
    "size" : 27487360,
    "uuid" : "1bce3526-51cf-3c0f-9dc2-6db21a78ef5b",
    "path" : "\/System\/Library\/Frameworks\/JavaScriptCore.framework\/Versions\/A\/JavaScriptCore",
    "name" : "JavaScriptCore",
    "CFBundleVersion" : "20621.********"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 7005011968,
    "CFBundleShortVersionString" : "20621",
    "CFBundleIdentifier" : "com.apple.WebKitLegacy",
    "size" : 2195808,
    "uuid" : "52cdc6c0-e3aa-3436-a3b8-44c92d26d6c7",
    "path" : "\/System\/Library\/Frameworks\/WebKit.framework\/Versions\/A\/Frameworks\/WebKitLegacy.framework\/Versions\/A\/WebKitLegacy",
    "name" : "WebKitLegacy",
    "CFBundleVersion" : "20621.********"
  }
],
  "sharedCache" : {
  "base" : 6528172032,
  "size" : 5047205888,
  "uuid" : "d7397d7f-8df9-3920-81a7-c0a144be9c51"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=1.8G resident=0K(0%) swapped_out_or_unallocated=1.8G(100%)\nWritable regions: Total=5.0G written=4084K(0%) resident=1124K(0%) swapped_out=2960K(0%) unallocated=5.0G(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nAccelerate framework               384K        3 \nActivity Tracing                   256K        1 \nCG image                          2192K       53 \nColorSync                          736K       35 \nCoreAnimation                     54.4M      837 \nCoreGraphics                        48K        3 \nCoreImage                         1536K       46 \nCoreUI image data                 5760K       44 \nFoundation                          48K        2 \nKernel Alloc Once                   32K        1 \nMALLOC                           801.7M       87 \nMALLOC guard page                  384K       24 \nSTACK GUARD                       56.4M       23 \nStack                             20.3M       43 \nVM_ALLOCATE                        2.1G       10 \nVM_ALLOCATE (reserved)             3.9G        1         reserved VM address space (unallocated)\nWebKit Malloc                    192.2M       10 \n__AUTH                            5475K      698 \n__AUTH_CONST                      76.6M      939 \n__CTF                               824        1 \n__DATA                            36.4M     1039 \n__DATA_CONST                      34.8M     1053 \n__DATA_DIRTY                      2766K      339 \n__FONT_DATA                        2352        1 \n__INFO_FILTER                         8        1 \n__LINKEDIT                       649.8M      112 \n__OBJC_RO                         61.4M        1 \n__OBJC_RW                         2396K        1 \n__TEXT                             1.2G     1038 \n__TEXT (graphics)                 58.0M       38 \n__TPRO_CONST                       128K        2 \ndyld private memory                256K        2 \nmapped file                      603.6M       93 \npage table in kernel              1124K        1 \nshared memory                     1488K       21 \n===========                     =======  ======= \nTOTAL                              9.8G     6603 \nTOTAL, minus reserved VM space     5.9G     6603 \n",
  "legacyInfo" : {
  "threadTriggered" : {
    "queue" : "com.apple.main-thread"
  }
},
  "logWritingSignature" : "fffad1cae906a9cbf9cb8c888dea65fba091d08e",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "60186475825c62000ccf5450",
      "factorPackIds" : {

      },
      "deploymentId" : 240000083
    },
    {
      "rolloutId" : "639124e81d92412bfb4880b3",
      "factorPackIds" : {

      },
      "deploymentId" : 240000012
    }
  ],
  "experiments" : [

  ]
}
}

Model: Mac15,13, BootROM 11881.121.1, proc 8:4:4 processors, 16 GB, SMC 
Graphics: Apple M3, Apple M3, Built-In
Display: Color LCD, spdisplays_2880x1864Retina, Main, MirrorOff, Online
Memory Module: LPDDR5, Hynix
AirPort: spairport_wireless_card_type_wifi (0x14E4, 0x4388), wl0: Mar 22 2025 02:16:34 version 23.40.30.0.41.51.180 FWID 01-5f726d72
IO80211_driverkit-1475.39 "IO80211_driverkit-1475.39" Apr 18 2025 20:10:40
AirPort: 
Bluetooth: Version (null), 0 services, 0 devices, 0 incoming serial ports
Network Service: Wi-Fi, AirPort, en0
USB Device: USB31Bus
USB Device: USB31Bus
Thunderbolt Bus: MacBook Air, Apple Inc.
Thunderbolt Bus: MacBook Air, Apple Inc.
