# 实现的思路

### 图片中问题的提取
- 目前qwen的调教已经很好了，可以针对特别异常的图片问题返回错误。直接拦截掉。保证用户图片规范
    比如将题目类型识别，若不存在则返回图片不完整重新拍摄
    比如针对判断题｜选择题的选项异常的时候返回图片不完整重新拍摄。

### 题干入库思路
- 选项的ABCD或者YN需要分字段保存这样便于后续的命中逻辑里对选项的命中

### 整个问题的命中思路
- 题干两个分词思路
    1. 逻辑短语对题干进行分词；（标点符号为间隔符进行分词）
    2. 滑动 n-gram（2～3字）交集并集比分词（可能要忽略标点符号，避免中英文标点符号对相似度产生影响）
- 检索思路
    1. 先用第一次的分词内容进行初步筛选；
    2. 在用第二次的分词对内容进行精准筛选；
    3. 选出相似度前3的结果；

- 选项匹配
    将目标问题的选项与题库内问题选项进行比对，至少保证3个选项完全一致。来做最终的结果确认；
    - 若存在多个符合条件的结果，则全部返回。（这里可能是因为问题与选项相同但答案不同的问题，因为有些问题是图片问题，目前ocr图片无法识别。）
    - 若不存在符合条件的结果，则提交大模型进行解答。

- 结论，即使极端情况下，相同问题，相同选项，也可以将所有结果正确返回。

此时筛选出的问题则为真，如果没有匹配则提交大模型进行解答。然后将解答出来的答案返回给用户并将内容入库；

题库问题需要人工核验，人工会对题库问题进行核验标记，确认正确后标记为已确认，上述步骤对数据题库命中时，只能在已确认的问题中命中。
